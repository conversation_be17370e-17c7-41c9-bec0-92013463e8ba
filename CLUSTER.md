# PM2 Cluster Configuration

This project includes a PM2 ecosystem configuration to run the OHLCV WebSocket API in cluster mode with 16 processes for optimal performance and reliability.

## 🚀 Quick Start

### Install Dependencies
```bash
npm install
```

### Start Cluster (Production)
```bash
npm run cluster
```

### Start Cluster (Development)
```bash
npm run cluster:dev
```

## 📋 Available Commands

| Command | Description |
|---------|-------------|
| `npm run cluster` | Start 16 processes in production mode |
| `npm run cluster:dev` | Start 16 processes in development mode |
| `npm run cluster:stop` | Stop all processes |
| `npm run cluster:restart` | Restart all processes (downtime) |
| `npm run cluster:reload` | Reload all processes (zero-downtime) |
| `npm run cluster:delete` | Delete all processes |
| `npm run cluster:logs` | View real-time logs |
| `npm run cluster:monit` | Open PM2 monitoring dashboard |
| `npm run cluster:status` | Show process status |

## ⚙️ Configuration Details

### Process Settings
- **Instances**: 16 processes
- **Mode**: Cluster (load balanced)
- **Memory Limit**: 1GB per process
- **Auto Restart**: Enabled
- **Max Restarts**: 10 attempts
- **Min Uptime**: 10 seconds

### Logging
- **Combined Logs**: `./logs/combined.log`
- **Output Logs**: `./logs/out.log`
- **Error Logs**: `./logs/error.log`
- **Log Rotation**: Automatic
- **Timestamp Format**: `YYYY-MM-DD HH:mm:ss Z`

### Health Monitoring
- **Memory Restart**: Automatic restart if process exceeds 1GB
- **Graceful Shutdown**: 5 second timeout
- **Health Check**: 3 second grace period
- **Restart Delay**: 4 seconds between restarts

## 🔧 Environment Variables

The cluster configuration supports different environments:

### Production (default)
```bash
NODE_ENV=production
PORT=3050
HOST=0.0.0.0
```

### Development
```bash
NODE_ENV=development
PORT=3050
HOST=0.0.0.0
```

## 📊 Monitoring

### Real-time Monitoring
```bash
npm run cluster:monit
```

### Process Status
```bash
npm run cluster:status
```

### Live Logs
```bash
npm run cluster:logs
```

### Log Files
- Check `./logs/` directory for persistent logs
- Logs are merged from all processes
- Automatic log rotation prevents disk space issues

## 🔄 Zero-Downtime Deployments

For production deployments without downtime:

```bash
# Deploy new code
git pull origin main
npm install

# Reload processes (zero-downtime)
npm run cluster:reload
```

## 🛠️ Troubleshooting

### High Memory Usage
- Processes automatically restart if they exceed 1GB
- Monitor with: `npm run cluster:monit`

### Process Crashes
- Auto-restart is enabled (max 10 attempts)
- Check error logs: `./logs/error.log`

### Port Conflicts
- Ensure port 3050 is available
- Modify `ecosystem.config.js` if needed

### Database Connections
- Each process maintains its own database connection pool
- Monitor connection limits in your PostgreSQL configuration

## 📈 Performance Benefits

### Load Distribution
- 16 processes share incoming requests
- Better CPU utilization on multi-core systems
- Improved concurrent request handling

### Fault Tolerance
- If one process crashes, others continue serving
- Automatic restart of failed processes
- Zero-downtime reloads for deployments

### Resource Management
- Memory limits prevent runaway processes
- Graceful shutdown ensures clean termination
- Health monitoring maintains system stability

## 🔐 Security Considerations

- Processes run with limited privileges
- Log files contain sensitive data - secure appropriately
- Monitor process resource usage
- Regular security updates for PM2 and dependencies 