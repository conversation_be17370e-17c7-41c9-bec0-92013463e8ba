# OHLCV WebSocket API v2.0 - Refactored

A high-performance, modular API built with Fastify for real-time OHLCV data and token information with bonding progress tracking.

## 🚀 Features

- **High Performance**: Built with Fastify for maximum speed and efficiency
- **Modular Architecture**: Clean separation of concerns with organized file structure
- **Type Safety**: Comprehensive JSON Schema validation for all endpoints
- **Real-time Data**: WebSocket support for live OHLCV updates
- **Token Bonding**: Advanced bonding progress calculations and metrics
- **Security**: Built-in rate limiting, CORS, and security headers
- **Documentation**: Auto-generated Swagger/OpenAPI documentation
- **Monitoring**: Health checks and structured logging

## 📁 Project Structure

```
liquidlabs-ohlcv-ws/
├── server-new.js              # Main server file (refactored)
├── server.js                  # Legacy server file
├── schemas/                   # JSON Schema definitions
│   ├── token.js              # Token-related schemas
│   └── common.js             # Shared schemas (pagination, errors)
├── routes/                    # Route handlers
│   ├── tokens.js             # Main token endpoints
│   ├── tokens-clean.js       # Clean token routes
│   ├── token-operations.js   # Token operations (swaps, burns, holders)
│   └── ohlcv.js             # OHLCV data endpoints
├── plugins/                   # Fastify plugins
│   ├── database.js           # Database connection plugin
│   └── websocket.js          # WebSocket functionality
├── utils/                     # Utility functions
│   └── tokenHelpers.js       # Token calculation helpers
├── package.json
└── README.md
```

## 🛠️ Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd liquidlabs-ohlcv-ws
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start the server:
```bash
# Development
npm run dev

# Production
npm start
```

## 🔧 Environment Variables

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ohlcv_db
DB_USER=ohlcv_user
DB_PASSWORD=your_secure_password_here
DB_SCHEMA=ll_history_schema

# Server Configuration
PORT=3050
HOST=0.0.0.0
NODE_ENV=production

# CORS Configuration
CORS_ORIGIN=*

# API Configuration
HYPE_API_URL=https://api.hyperliquid.xyz/info
```

## 📚 API Documentation

The API provides comprehensive documentation through Swagger UI:

- **Development**: http://localhost:3050/documentation
- **Health Check**: http://localhost:3050/health

## 🎯 Key Endpoints

### Token Information
- `GET /api/token/:tokenAddress` - Get detailed token information
- `GET /api/tokens` - List tokens with filtering and sorting
- `GET /api/tokens/search` - Search tokens by name, symbol, or address

### Token Operations
- `GET /api/token/:tokenAddress/swaps` - Get token swap history
- `GET /api/token/:tokenAddress/burns` - Get token burn events
- `GET /api/token/:tokenAddress/holders` - Get token holder balances

### OHLCV Data
- `GET /api/ohlcv/:address` - Get historical OHLCV data

### System
- `GET /health` - Health check endpoint

## 🔄 Schema Design

### Unified Token Schema
All token endpoints use a consistent schema that includes:

- **Basic Information**: address, name, symbol, decimals
- **Metadata**: social links, images, descriptions
- **Financial Data**: price, market cap, liquidity, volume
- **Bonding Information**: progress, HYPE requirements, status
- **Holder Data**: top holders, distribution percentages

### Request/Response Validation
- All endpoints have comprehensive JSON Schema validation
- Consistent error responses across all endpoints
- Proper HTTP status codes and error messages

## 🏗️ Architecture Benefits

### 1. **Modularity**
- Separated concerns into logical modules
- Reusable schemas across endpoints
- Plugin-based architecture for extensibility

### 2. **Performance**
- Fastify's high-performance HTTP server
- Optimized database queries
- Built-in compression and caching headers

### 3. **Security**
- Rate limiting to prevent abuse
- CORS configuration for cross-origin requests
- Security headers via Helmet
- Input validation and sanitization

### 4. **Maintainability**
- Clear file organization
- Consistent coding patterns
- Comprehensive error handling
- Structured logging

### 5. **Developer Experience**
- Auto-generated API documentation
- Type-safe schemas
- Hot-reload in development
- Comprehensive error messages

## 🧪 Testing

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Linting
npm run lint
npm run lint:fix
```

## 📊 Token Bonding Logic

The API calculates bonding progress based on:

- **Initial Supply**: 1B tokens
- **Initial HYPE**: 300 HYPE
- **Bonding Threshold**: 90% tokens sold
- **HYPE for 100%**: 30 HYPE (10% of initial)

### Bonding Calculations
1. **Progress**: `(tokensSold / totalSupply) * 100`
2. **HYPE Progress**: `(currentHype / hypeNeededFor100) * 100`
3. **Bonded Status**: `tokensSoldPercentage >= 90%`

## 🔍 Real-time Features

### WebSocket Events
- `subscribe` - Subscribe to OHLCV updates for a wallet
- `unsubscribe` - Unsubscribe from updates
- `join_address_txs` - Join transaction updates for an address
- `leave_address_txs` - Leave transaction updates

### Real-time Data Sources
- Token prices from Hyperliquid API
- Real-time holder counts and balances
- Live swap and burn events
- Market cap and liquidity calculations

## 🚀 Deployment

### Production Considerations
1. Set `NODE_ENV=production`
2. Configure proper CORS origins
3. Set up database connection pooling
4. Enable logging aggregation
5. Configure rate limiting appropriately
6. Set up health check monitoring

### Docker Support
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3050
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Add tests for new functionality
5. Run linting: `npm run lint:fix`
6. Commit your changes: `git commit -am 'Add feature'`
7. Push to the branch: `git push origin feature-name`
8. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

## Migration from v1.0

To migrate from the legacy server:

1. Update your package.json dependencies
2. Use `server-new.js` instead of `server.js`
3. Update any custom integrations to use the new schema format
4. Test all endpoints with the new response structure
5. Update your frontend to handle the consistent token schema

The new API maintains backward compatibility for core functionality while providing enhanced features and better performance.
