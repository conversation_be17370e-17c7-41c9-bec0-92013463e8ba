// debug-server.js - Simple diagnostic server to test each component
const fastify = require('fastify')({
  logger: {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true
      }
    }
  }
});

require('dotenv').config();

async function testServer() {
  try {
    console.log('🔍 Starting diagnostic server...');

    // Test 1: Basic server setup
    console.log('✅ Step 1: Basic server setup - OK');

    // Test 2: CORS registration
    await fastify.register(require('@fastify/cors'), {
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST'],
      credentials: true
    });
    console.log('✅ Step 2: CORS registration - OK');

    // Test 3: Database plugin
    console.log('🔍 Step 3: Testing database plugin...');
    await fastify.register(require('./plugins/database'));
    console.log('✅ Step 3: Database plugin - OK');

    // Test 4: Simple health endpoint
    fastify.get('/health', async (request, reply) => {
      try {
        await fastify.pool.query('SELECT 1');
        return { status: 'healthy', timestamp: new Date().toISOString() };
      } catch (error) {
        reply.code(503);
        return { status: 'unhealthy', error: error.message };
      }
    });
    console.log('✅ Step 4: Health endpoint - OK');

    // Test 5: Start server
    const port = process.env.PORT || 3051; // Different port for testing
    const host = process.env.HOST || '0.0.0.0';

    console.log('🔍 Step 5: Starting server...');
    await fastify.listen({ port, host });
    
    console.log(`✅ Diagnostic server running on http://${host}:${port}`);
    console.log('✅ All basic components working! Testing health endpoint...');

    // Keep server running for a bit to see if it stays stable
    setTimeout(() => {
      console.log('✅ Server stable for 10 seconds - basic setup is working');
      process.exit(0);
    }, 10000);

  } catch (error) {
    console.error('❌ Error during diagnostic:', error);
    process.exit(1);
  }
}

// Graceful shutdown handlers
process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down diagnostic server...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down diagnostic server...');
  process.exit(0);
});

process.on('uncaughtException', (err) => {
  console.error('❌ Uncaught exception in diagnostic:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection in diagnostic:', reason);
  process.exit(1);
});

testServer(); 