module.exports = {
  apps: [
    {
      name: 'liquidlabs-ohlcv-api',
      script: 'server-new.js',
      instances: 16,
      exec_mode: 'cluster',
      
      // Environment variables
      env: {
        NODE_ENV: 'production',
        PORT: 3050,
        HOST: '0.0.0.0'
      },
      
      // Development environment
      env_development: {
        NODE_ENV: 'development',
        PORT: 3050,
        HOST: '0.0.0.0'
      },
      
      // Restart policy
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Logging
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Advanced settings
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      
      // Health monitoring
      health_check_grace_period: 3000,
      
      // Instance settings for cluster mode
      instance_var: 'INSTANCE_ID',
      
      // Source map support
      source_map_support: true,
      
      // Graceful shutdown
      kill_retry_time: 100,
      
      // Process title
      name: 'liquidlabs-ohlcv-api'
    }
  ],
  
  // Deployment configuration (optional)
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server.com'],
      ref: 'origin/main',
      repo: 'https://github.com/liquidlabs/ohlcv-ws-api.git',
      path: '/var/www/liquidlabs-ohlcv-api',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    }
  }
}; 