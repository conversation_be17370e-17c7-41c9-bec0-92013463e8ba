const { Pool } = require('pg');
require('dotenv').config();

// PostgreSQL connection
const pool = new Pool({
  user: process.env.DB_USER || 'ohlcv_user',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'ohlcv_db',
  password: process.env.DB_PASSWORD || 'your_secure_password_here',
  port: process.env.DB_PORT || 5432,
});

const dbSchema = process.env.DB_SCHEMA || 'll_history_schema';

// Set schema path for the database connection
pool.on('connect', (client) => {
  client.query(`SET search_path TO ${dbSchema}, public`);
});

async function getHoldersAtBlock(tokenAddress, blockHeight) {
  if (!tokenAddress || !/^0x[a-fA-F0-9]{40}$/.test(tokenAddress)) {
    throw new Error('Invalid token address');
  }

  if (!blockHeight || isNaN(blockHeight) || blockHeight < 0) {
    throw new Error('Invalid block height');
  }

  try {
    // First, let's check if there are any swaps for this token
    const swapCheckQuery = `
      SELECT 
        MIN(block_number::numeric) as first_block,
        MAX(block_number::numeric) as last_block,
        COUNT(*) as total_swaps
      FROM ${dbSchema}.swaps
      WHERE token = $1
    `;
    const swapCheck = await pool.query(swapCheckQuery, [tokenAddress]);
    console.log('Swap check:', swapCheck.rows[0]);

    // Get all swaps up to the specified block height
    const query = `
      WITH swap_balances AS (
        SELECT 
          trader as address,
          CASE 
            WHEN type = 'purchase' THEN token_amount::numeric
            WHEN type = 'sale' THEN -token_amount::numeric
          END as amount,
          block_number::numeric as block_number
        FROM ${dbSchema}.swaps
        WHERE token = $1
          AND block_number::numeric <= $2
      ),
      holder_balances AS (
        SELECT 
          address,
          SUM(amount) as balance,
          MAX(block_number) as last_swap_block
        FROM swap_balances
        WHERE address != '0x0000000000000000000000000000000000000000'
        GROUP BY address
        HAVING SUM(amount) > 0
      )
      SELECT 
        address,
        balance::text,
        last_swap_block,
        ROW_NUMBER() OVER (ORDER BY balance DESC) as rank
      FROM holder_balances
      ORDER BY balance DESC
    `;

    const result = await pool.query(query, [tokenAddress, blockHeight]);
    console.log('Number of holders found:', result.rows.length);

    // Get token info
    const tokenQuery = `
      SELECT 
        name,
        symbol,
        decimals,
        metadata->>'image_uri' as image_uri,
        creation_timestamp,
        creator
      FROM ${dbSchema}.tokens
      WHERE address = $1
    `;
    const tokenResult = await pool.query(tokenQuery, [tokenAddress]);

    if (tokenResult.rows.length === 0) {
      throw new Error('Token not found');
    }

    const token = tokenResult.rows[0];
    const totalHolders = result.rows.length;
    const totalBalance = result.rows.reduce((sum, holder) => sum + parseFloat(holder.balance), 0);

    // Calculate top 10 holders percentage
    const top10Balance = result.rows
      .slice(0, 10)
      .reduce((sum, holder) => sum + parseFloat(holder.balance), 0);
    const top10Percentage = totalBalance > 0 ? ((top10Balance / totalBalance) * 100).toFixed(2) : '0.00';

    // Get the first swap block for this token
    const firstSwapQuery = `
      SELECT MIN(block_number::numeric) as first_block
      FROM ${dbSchema}.swaps
      WHERE token = $1
    `;
    const firstSwapResult = await pool.query(firstSwapQuery, [tokenAddress]);
    const firstSwapBlock = firstSwapResult.rows[0]?.first_block;

    return {
      token: {
        address: tokenAddress,
        name: token.name,
        symbol: token.symbol,
        decimals: token.decimals,
        image_uri: token.image_uri,
        creator: token.creator,
        creation_timestamp: token.creation_timestamp
      },
      blockHeight: parseInt(blockHeight),
      firstSwapBlock: firstSwapBlock ? parseInt(firstSwapBlock) : null,
      totalHolders,
      totalBalance: totalBalance.toString(),
      top10HoldersPercentage: `${top10Percentage}%`,
      holders: result.rows.map(holder => ({
        address: holder.address,
        balance: holder.balance,
        percentage: totalBalance > 0 ? ((parseFloat(holder.balance) / totalBalance) * 100).toFixed(2) + '%' : '0.00%',
        rank: holder.rank,
        lastSwapBlock: parseInt(holder.last_swap_block)
      }))
    };
  } catch (error) {
    console.error('Error fetching holders at block:', error);
    throw error;
  }
}

// Example usage
async function main() {
  try {
    const tokenAddress = process.argv[2];
    const blockHeight = process.argv[3];

    if (!tokenAddress || !blockHeight) {
      console.log('Usage: node getHoldersAtBlock.js <tokenAddress> <blockHeight>');
      process.exit(1);
    }

    const result = await getHoldersAtBlock(tokenAddress, blockHeight);
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}

module.exports = { getHoldersAtBlock }; 