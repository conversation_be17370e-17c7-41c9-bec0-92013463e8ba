const { errorSchema, tokenAddressParam, paginationQuery } = require('../schemas/common');
const { isValidTokenAddress, getHypePrice } = require('../utils/tokenHelpers');

async function tokenOperationsRoutes(fastify, options) {
  const { pool } = fastify;
  const dbSchema = process.env.DB_SCHEMA || 'll_history_schema';

  // Get token swaps with pagination
  fastify.get('/api/token/:tokenAddress/swaps', {
    schema: {
      description: `Get paginated swap history for a token.
      
      Real-time Data:
      - Swap amounts are converted to USD using current HYPE price
      - Token reserves and HYPE reserves are from real-time data`,
      tags: ['token'],
      summary: 'Get token swap history with real-time conversions',
      params: tokenAddressParam,
      querystring: {
        allOf: [
          paginationQuery,
          {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                description: 'Filter by swap type (purchase or sale)',
                enum: ['purchase', 'sale']
              }
            }
          }
        ]
      },
      response: {
        200: {
          description: 'Successful response',
          type: 'object',
          properties: {
            pagination: {
              type: 'object',
              properties: {
                currentPage: { type: 'integer' },
                totalPages: { type: 'integer' },
                totalSwaps: { type: 'integer' },
                limit: { type: 'integer' }
              }
            },
            swaps: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'integer' },
                  token: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
                  type: { type: 'string', enum: ['purchase', 'sale'] },
                  trader: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
                  hype_amount: { type: 'string' },
                  token_amount: { type: 'string' },
                  price: { type: 'string' },
                  timestamp: { type: 'integer' },
                  tx_hash: { type: 'string', pattern: '^0x[a-fA-F0-9]{64}$' },
                  market_cap: {
                    type: 'object',
                    properties: {
                      hype: { type: 'string' },
                      usd: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        },
        400: {
          description: 'Bad request',
          ...errorSchema
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const { tokenAddress } = request.params;
    const { page = 1, limit = 20, type } = request.query;

    if (!isValidTokenAddress(tokenAddress)) {
      return reply.code(400).send({ error: 'Invalid token address format' });
    }

    if (page < 1 || limit < 1 || limit > 100) {
      return reply.code(400).send({ error: 'Invalid pagination parameters' });
    }

    if (type && !['purchase', 'sale'].includes(type)) {
      return reply.code(400).send({ error: 'Invalid swap type' });
    }

    const offset = (page - 1) * limit;

    // Define queries outside the try block
    let swapsQuery = '';
    let countQuery = '';
    let queryParams = [tokenAddress];

    try {
      // Build the queries
      swapsQuery = `
        SELECT 
          id,
          token,
          type,
          trader,
          hype_amount,
          token_amount,
          price,
          timestamp,
          tx_hash,
          COALESCE(total_supply::numeric, 0) as total_supply,
          COALESCE(price::numeric * total_supply::numeric, 0) as market_cap_hype
        FROM ${dbSchema}.swaps
        WHERE token = $1
      `;

      countQuery = `
        SELECT COUNT(*)
        FROM ${dbSchema}.swaps
        WHERE token = $1
      `;

      if (type) {
        swapsQuery += ` AND type = $${queryParams.length + 1}`;
        countQuery += ` AND type = $${queryParams.length + 1}`;
        queryParams.push(type);
      }

      swapsQuery += `
        ORDER BY timestamp DESC, id DESC
        LIMIT $${queryParams.length + 1}
        OFFSET $${queryParams.length + 2}
      `;
      queryParams.push(limit, offset);

      // Log the query and params for debugging
      fastify.log.debug('Swaps query:', { 
        query: swapsQuery, 
        params: queryParams,
        schema: dbSchema 
      });

      const [swapsResult, countResult] = await Promise.all([
        pool.query(swapsQuery, queryParams),
        pool.query(countQuery, [tokenAddress, ...(type ? [type] : [])])
      ]);

      const totalSwaps = parseInt(countResult.rows[0].count, 10);
      const totalPages = Math.ceil(totalSwaps / limit);

      // Get current HYPE price for USD conversion
      let hypePriceFloat = 0;
      try {
        const hypePrice = await getHypePrice();
        hypePriceFloat = parseFloat(hypePrice);
        fastify.log.debug('HYPE price retrieved:', { price: hypePriceFloat });
      } catch (error) {
        fastify.log.warn('Failed to get HYPE price, using 0 for USD conversion:', error);
      }

      // Transform the swaps to include USD values
      const swaps = swapsResult.rows.map(swap => ({
        ...swap,
        market_cap: {
          hype: swap.market_cap_hype?.toString() || '0',
          usd: ((parseFloat(swap.market_cap_hype || 0) * hypePriceFloat)).toFixed(2)
        }
      }));

      return {
        pagination: {
          currentPage: page,
          totalPages,
          totalSwaps,
          limit
        },
        swaps
      };

    } catch (error) {
      fastify.log.error('Error fetching token swaps:', {
        error: error.message,
        stack: error.stack,
        query: swapsQuery,
        params: queryParams,
        schema: dbSchema
      });
      return reply.code(500).send({ 
        error: 'Failed to fetch token swaps',
        details: error.message
      });
    }
  });

  // Get token burns with pagination
  fastify.get('/api/token/:tokenAddress/burns', {
    schema: {
      description: 'Get paginated token burn events',
      tags: ['token'],
      summary: 'Get token burn history',
      params: tokenAddressParam,
      querystring: paginationQuery,
      response: {
        200: {
          description: 'Successful response',
          type: 'object',
          properties: {
            pagination: {
              type: 'object',
              properties: {
                currentPage: { type: 'integer' },
                totalPages: { type: 'integer' },
                totalBurns: { type: 'integer' },
                limit: { type: 'integer' }
              }
            },
            burns: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'integer' },
                  token: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
                  amount: { type: 'string' },
                  timestamp: { type: 'integer' },
                  tx_hash: { type: 'string', pattern: '^0x[a-fA-F0-9]{64}$' }
                }
              }
            }
          }
        },
        400: {
          description: 'Bad request',
          ...errorSchema
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const { tokenAddress } = request.params;
    const { page = 1, limit = 20 } = request.query;

    if (!isValidTokenAddress(tokenAddress)) {
      return reply.code(400).send({ error: 'Invalid token address format' });
    }

    if (page < 1 || limit < 1 || limit > 100) {
      return reply.code(400).send({ error: 'Invalid pagination parameters' });
    }

    const offset = (page - 1) * limit;

    try {
      const burnsQuery = `
        SELECT *
        FROM ${dbSchema}.token_burned_events
        WHERE token = $1
        ORDER BY timestamp DESC, id DESC
        LIMIT $2
        OFFSET $3
      `;

      const countQuery = `
        SELECT COUNT(*)
        FROM ${dbSchema}.token_burned_events
        WHERE token = $1
      `;

      const [burnsResult, countResult] = await Promise.all([
        pool.query(burnsQuery, [tokenAddress, limit, offset]),
        pool.query(countQuery, [tokenAddress])
      ]);

      const totalBurns = parseInt(countResult.rows[0].count, 10);
      const totalPages = Math.ceil(totalBurns / limit);

      return {
        pagination: {
          currentPage: page,
          totalPages,
          totalBurns,
          limit
        },
        burns: burnsResult.rows
      };

    } catch (error) {
      fastify.log.error('Error fetching token burns:', error);
      return reply.code(500).send({ error: 'Failed to fetch token burns' });
    }
  });

  // Get token holders with pagination
  fastify.get('/api/token/:tokenAddress/holders', {
    schema: {
      description: `Get current token holder balances with USD values.
      
      Real-time Data:
      - Holder balances are fetched from real-time tables
      - USD values are calculated using current HYPE price
      - Total supply and holder count are from real-time data`,
      tags: ['token'],
      summary: 'Get token holder balances with real-time data',
      params: tokenAddressParam,
      querystring: {
        allOf: [
          paginationQuery,
          {
            type: 'object',
            properties: {
              min_balance: {
                type: 'string',
                description: 'Minimum token balance to include (in token units)',
                default: '0'
              }
            }
          }
        ]
      },
      response: {
        200: {
          description: 'Successful response',
          type: 'object',
          properties: {
            pagination: {
              type: 'object',
              properties: {
                currentPage: { type: 'integer' },
                totalPages: { type: 'integer' },
                totalHolders: { type: 'integer' },
                limit: { type: 'integer' }
              }
            },
            holders: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
                  balance: { type: 'string' },
                  percentage: { type: 'string' }
                }
              }
            }
          }
        },
        400: {
          description: 'Bad request',
          ...errorSchema
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const { tokenAddress } = request.params;
    const { page = 1, limit = 20, min_balance = '0' } = request.query;

    if (!isValidTokenAddress(tokenAddress)) {
      return reply.code(400).send({ error: 'Invalid token address format' });
    }

    if (page < 1 || limit < 1 || limit > 100) {
      return reply.code(400).send({ error: 'Invalid pagination parameters' });
    }

    const offset = (page - 1) * limit;

    try {
      // Get total supply for percentage calculations
      const totalSupplyQuery = `
        SELECT total_supply
        FROM ${dbSchema}.realtime_token_data
        WHERE token_address = $1
      `;
      const totalSupplyResult = await pool.query(totalSupplyQuery, [tokenAddress]);
      const totalSupply = parseFloat(totalSupplyResult.rows[0]?.total_supply || '0');

      // Get holders with pagination
      const holdersQuery = `
        SELECT 
          holder_address as address,
          balance,
          ROUND((balance * 100.0 / NULLIF($4::numeric, 0))::numeric, 4) as percentage
        FROM ${dbSchema}.token_holders
        WHERE token_address = $1 
          AND balance >= $2::numeric
        ORDER BY balance DESC
        LIMIT $3
        OFFSET $5
      `;

      const countQuery = `
        SELECT COUNT(*)
        FROM ${dbSchema}.token_holders
        WHERE token_address = $1 
          AND balance >= $2::numeric
      `;

      const [holdersResult, countResult] = await Promise.all([
        pool.query(holdersQuery, [tokenAddress, min_balance, limit, totalSupply, offset]),
        pool.query(countQuery, [tokenAddress, min_balance])
      ]);

      const totalHolders = parseInt(countResult.rows[0].count, 10);
      const totalPages = Math.ceil(totalHolders / limit);

      // Format percentage for display
      const holders = holdersResult.rows.map(holder => ({
        ...holder,
        percentage: holder.percentage ? `${holder.percentage}%` : '0%'
      }));

      return {
        pagination: {
          currentPage: page,
          totalPages,
          totalHolders,
          limit
        },
        holders
      };

    } catch (error) {
      fastify.log.error('Error fetching token holders:', error);
      return reply.code(500).send({ error: 'Failed to fetch token holders' });
    }
  });

  // Get all token holders (simplified endpoint without pagination)
  fastify.get('/api/token/:tokenAddress/all-holders', {
    schema: {
      description: `Get token holders and their balances with optional limit.
      
      This endpoint returns holders with an optional limit for performance. 
      
      Real-time Data:
      - Holder balances are fetched from real-time tables
      - Balances are formatted using the token's decimal places`,
      tags: ['token'],
      summary: 'Get token holders with optional limit',
      params: tokenAddressParam,
      querystring: {
        type: 'object',
        properties: {
          limit: {
            type: 'integer',
            description: 'Maximum number of holders to return (1-1000)',
            default: 100,
            minimum: 1,
            maximum: 1000
          },
          min_balance: {
            type: 'string',
            description: 'Minimum token balance to include (in token units)',
            default: '0'
          },
          include_percentage: {
            type: 'boolean',
            description: 'Include percentage of total supply for each holder',
            default: false
          }
        }
      },
      response: {
        200: {
          description: 'Successful response',
          type: 'object',
          properties: {
            token_address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
            total_holders_returned: { type: 'integer' },
            total_supply: { type: 'string' },
            holders: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
                  balance: { type: 'string' },
                  percentage: { type: 'string' }
                }
              }
            }
          }
        },
        400: {
          description: 'Bad request',
          ...errorSchema
        },
        404: {
          description: 'Token not found',
          ...errorSchema
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const { tokenAddress } = request.params;
    const { limit = 100, min_balance = '0', include_percentage = false } = request.query;

    if (!isValidTokenAddress(tokenAddress)) {
      return reply.code(400).send({ error: 'Invalid token address format' });
    }

    if (limit < 1 || limit > 1000) {
      return reply.code(400).send({ error: 'Limit must be between 1 and 1000' });
    }

    try {
      // First check if token exists (lightweight query)
      const tokenExistsQuery = `
        SELECT address, decimals, name, symbol
        FROM ${dbSchema}.tokens
        WHERE address = $1
      `;
      const tokenResult = await pool.query(tokenExistsQuery, [tokenAddress]);
      
      if (tokenResult.rows.length === 0) {
        return reply.code(404).send({ error: 'Token not found' });
      }

      const token = tokenResult.rows[0];

      // Optimize query based on whether percentage is needed
      let holdersQuery;
      let queryParams = [tokenAddress, min_balance, limit];
      
      if (include_percentage) {
        // Only get total supply if percentage calculation is needed
        holdersQuery = `
          WITH token_supply AS (
            SELECT COALESCE(total_supply, 0) as total_supply
            FROM ${dbSchema}.realtime_token_data
            WHERE token_address = $1
          )
          SELECT 
            th.holder_address as address,
            th.balance,
            CASE 
              WHEN ts.total_supply > 0 THEN 
                ROUND((th.balance * 100.0 / ts.total_supply)::numeric, 4)
              ELSE 0
            END as percentage
          FROM ${dbSchema}.token_holders th
          CROSS JOIN token_supply ts
          WHERE th.token_address = $1 
            AND th.balance >= $2::numeric
          ORDER BY th.balance DESC
          LIMIT $3
        `;
      } else {
        // Simpler query without percentage calculation for better performance
        holdersQuery = `
          SELECT 
            holder_address as address,
            balance
          FROM ${dbSchema}.token_holders
          WHERE token_address = $1 
            AND balance >= $2::numeric
          ORDER BY balance DESC
          LIMIT $3
        `;
      }

      const holdersResult = await pool.query(holdersQuery, queryParams);

      // Format the response
      const holders = holdersResult.rows.map(holder => ({
        address: holder.address,
        balance: holder.balance,
        ...(include_percentage && { percentage: holder.percentage ? `${holder.percentage}%` : '0%' })
      }));

      // Get total supply only if percentage was calculated (for response metadata)
      let totalSupply = '0';
      if (include_percentage && holdersResult.rows.length > 0) {
        // We already have it from the query, extract from first row calculation
        const totalSupplyQuery = `
          SELECT COALESCE(total_supply, 0) as total_supply
          FROM ${dbSchema}.realtime_token_data
          WHERE token_address = $1
        `;
        const totalSupplyResult = await pool.query(totalSupplyQuery, [tokenAddress]);
        totalSupply = totalSupplyResult.rows[0]?.total_supply || '0';
      }

      return {
        token_address: tokenAddress,
        token_info: {
          name: token.name,
          symbol: token.symbol,
          decimals: token.decimals
        },
        total_holders_returned: holders.length,
        limit_applied: limit,
        total_supply: totalSupply,
        holders
      };

    } catch (error) {
      fastify.log.error('Error fetching token holders:', error);
      return reply.code(500).send({ error: 'Failed to fetch token holders' });
    }
  });
}

module.exports = tokenOperationsRoutes; 