const { errorSchema } = require('../schemas/common');

async function swapsRoutes(fastify, options) {
  const { pool } = fastify;
  const dbSchema = process.env.DB_SCHEMA || 'll_history_schema';

  // Get latest swaps across all tokens
  fastify.get('/api/swaps/latest', {
    schema: {
      description: 'Get latest swaps across all tokens with significant volume (>= 0.5 HYPE)',
      tags: ['swaps'],
      summary: 'Get latest swaps with token details',
      querystring: {
        type: 'object',
        properties: {
          limit: {
            type: 'integer',
            description: 'Number of swaps to return',
            default: 50,
            minimum: 1,
            maximum: 500
          }
        }
      },
      response: {
        200: {
          description: 'Successful response',
          type: 'array',
          items: {
            type: 'object',
            properties: {
              tx_hash: { 
                type: 'string', 
                pattern: '^0x[a-fA-F0-9]{64}$',
                description: 'Transaction hash'
              },
              token: { 
                type: 'string', 
                pattern: '^0x[a-fA-F0-9]{40}$',
                description: 'Token contract address'
              },
              timestamp: { 
                type: 'integer',
                description: 'Unix timestamp of the swap'
              },
              amount: { 
                type: 'string', 
                format: 'decimal',
                description: 'Token amount swapped'
              },
              hype_amount: { 
                type: 'string', 
                format: 'decimal',
                description: 'HYPE amount in the swap'
              },
              price: { 
                type: 'string', 
                format: 'decimal',
                description: 'Price per token in HYPE'
              },
              trader: { 
                type: 'string', 
                pattern: '^0x[a-fA-F0-9]{40}$',
                description: 'Trader wallet address'
              },
              swap_type: { 
                type: 'string',
                enum: ['buy', 'sell'],
                description: 'Type of swap: "buy" for purchases, "sell" for sales'
              },
              symbol: { 
                type: 'string',
                description: 'Token symbol'
              },
              name: { 
                type: 'string',
                description: 'Token name'
              },
              has_creator_sold: { 
                type: 'boolean',
                description: 'Whether the token creator has sold any tokens'
              },
              image_uri: { 
                type: ['string', 'null'],
                description: 'Token image URI'
              }
            },
            required: ['tx_hash', 'token', 'timestamp', 'amount', 'hype_amount', 'price', 'trader', 'swap_type', 'symbol', 'name', 'has_creator_sold']
          }
        },
        400: {
          description: 'Bad request',
          ...errorSchema
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const { limit = 50 } = request.query;

    // Validate limit
    if (limit < 1 || limit > 500) {
      return reply.code(400).send({ error: 'Limit must be between 1 and 500' });
    }

    try {
      const query = `
        SELECT 
          s.tx_hash,
          s.token,
          s.timestamp,
          s.token_amount as amount,
          s.hype_amount,
          s.price,
          s.trader,
          CASE WHEN s.type = 'purchase' THEN 'buy' ELSE 'sell' END as swap_type,
          t.symbol,
          t.name,
          CASE 
            WHEN EXISTS (
              SELECT 1 FROM ${dbSchema}.swaps 
              WHERE token = t.address 
              AND trader = t.creator 
              AND type = 'sale'
            ) THEN true 
            ELSE false 
          END as has_creator_sold,
          t.metadata->>'image_uri' as image_uri
        FROM ${dbSchema}.swaps s
        JOIN ${dbSchema}.tokens t ON s.token = t.address
        WHERE s.hype_amount >= 0.5
        ORDER BY s.timestamp DESC
        LIMIT $1
      `;

      const result = await pool.query(query, [limit]);
      return result.rows;
    } catch (error) {
      fastify.log.error('Error fetching latest swaps. Limit: %s, Message: %s, Stack: %s, FullError: %j', 
        limit, error.message, error.stack, error);
      return reply.code(500).send({ error: 'Failed to fetch latest swaps' });
    }
  });
}

module.exports = swapsRoutes; 