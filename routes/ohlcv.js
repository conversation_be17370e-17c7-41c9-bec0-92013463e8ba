const { errorSchema } = require('../schemas/common');

// Helper function to parse human-readable date strings
function parseHumanDate(dateString) {
  if (!dateString) return null;
  
  const now = new Date();
  const lowerDate = dateString.toLowerCase().trim();
  
  // Handle "now" or similar
  if (lowerDate === 'now' || lowerDate === 'current') {
    return now.toISOString();
  }
  
  // Handle relative dates like "7 days ago", "1 hour ago", etc.
  const relativeMatch = lowerDate.match(/^(\d+)\s+(second|minute|hour|day|week|month|year)s?\s+ago$/);
  if (relativeMatch) {
    const [, amount, unit] = relativeMatch;
    const value = parseInt(amount);
    
    const date = new Date(now);
    switch (unit) {
      case 'second':
        date.setSeconds(date.getSeconds() - value);
        break;
      case 'minute':
        date.setMinutes(date.getMinutes() - value);
        break;
      case 'hour':
        date.setHours(date.getHours() - value);
        break;
      case 'day':
        date.setDate(date.getDate() - value);
        break;
      case 'week':
        date.setDate(date.getDate() - (value * 7));
        break;
      case 'month':
        date.setMonth(date.getMonth() - value);
        break;
      case 'year':
        date.setFullYear(date.getFullYear() - value);
        break;
    }
    return date.toISOString();
  }
  
  // Try to parse as regular date string
  try {
    const parsedDate = new Date(dateString);
    if (!isNaN(parsedDate.getTime())) {
      return parsedDate.toISOString();
    }
  } catch (error) {
    // Fall through to default
  }
  
  // If we can't parse it, return null to use default
  return null;
}

async function ohlcvRoutes(fastify, options) {
  const { pool } = fastify;

  // REST endpoint to get historical OHLCV data for a wallet address
  fastify.get('/api/ohlcv/:address', {
    schema: {
      description: 'Get historical OHLCV data for a wallet address',
      tags: ['ohlcv'],
      summary: 'Get OHLCV data for a wallet address',
      params: {
        type: 'object',
        required: ['address'],
        properties: {
          address: {
            type: 'string',
            description: 'Wallet address to get OHLCV data for'
          }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          interval: {
            type: 'string',
            description: 'Time interval for OHLCV data (e.g., 1h, 1d)',
            default: '1h'
          },
          start: {
            type: 'string',
            description: 'Start time (ISO format, or human-readable like "7 days ago")',
            default: '7 days ago'
          },
          end: {
            type: 'string',
            description: 'End time (ISO format, or human-readable like "now")',
            default: 'now'
          },
          chain: {
            type: 'string',
            description: 'Blockchain network (e.g., ethereum, bitcoin)'
          }
        }
      },
      response: {
        200: {
          description: 'Successful response',
          type: 'array',
          items: {
            type: 'object',
            properties: {
              time: { type: 'string', format: 'date-time' },
              open: { type: 'number' },
              high: { type: 'number' },
              low: { type: 'number' },
              close: { type: 'number' },
              volume: { type: 'number' }
            }
          }
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const { address } = request.params;
    const { interval, start, end, chain } = request.query;

    try {
      const timeInterval = interval || '1h';
      
      // Parse start time - try human-readable first, fall back to default
      const parsedStart = parseHumanDate(start);
      const startTime = parsedStart || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
      
      // Parse end time - try human-readable first, fall back to default
      const parsedEnd = parseHumanDate(end);
      const endTime = parsedEnd || new Date().toISOString();

      const query = `
        SELECT 
          time_bucket($1, timestamp) AS time,
          FIRST(open, timestamp) AS open,
          MAX(high) AS high,
          MIN(low) AS low,
          LAST(close, timestamp) AS close,
          SUM(volume) AS volume
        FROM ${process.env.DB_SCHEMA || 'ohlcv_schema'}.price_data
        WHERE address = $2 AND timestamp >= $3 AND timestamp <= $4
        GROUP BY time
        ORDER BY time
      `;

      const result = await pool.query(query, [timeInterval, address, startTime, endTime]);
      return result.rows;
    } catch (error) {
      fastify.log.error('Error fetching OHLCV data. Address: %s, Interval: %s, Start: %s, End: %s, Message: %s, Stack: %s, FullError: %j', 
        address, timeInterval, startTime, endTime, error.message, error.stack, error);
      return reply.code(500).send({ error: 'Failed to fetch data' });
    }
  });
}

module.exports = ohlcvRoutes; 