const { tokenSchema, searchTokenSchema } = require('../schemas/token');
const {
  paginationSchema,
  errorSchema,
  tokenAddressParam,
  paginationQuery,
  tokenSortQuery,
  tokenFilterQuery,
  searchQuery,
  timeframeQuery
} = require('../schemas/common');
const { getHypePrice, transformTokenData, isValidTokenAddress } = require('../utils/tokenHelpers');

async function tokenRoutes(fastify, options) {
  const { pool } = fastify;
  const dbSchema = process.env.DB_SCHEMA || 'll_history_schema';

  // Get single token details
  fastify.get('/api/token/:tokenAddress', {
    schema: {
      description: `Get detailed token information including bonding progress and trading statistics.
      
      Real-time Data:
      - Market cap, price, holder count, and supply data are fetched from real-time tables
      - All financial calculations use real-time data for accuracy
      - Bonding progress is calculated based on current token reserves and HYPE in pool
      - Trading statistics include buy/sell volumes, unique traders, and price changes over specified timeframe`,
      tags: ['token'],
      summary: 'Get token details with bonding progress and trading stats',
      params: tokenAddressParam,
      querystring: timeframeQuery,
      response: {
        200: {
          description: 'Successful response',
          ...tokenSchema
        },
        400: {
          description: 'Bad request',
          ...errorSchema
        },
        404: {
          description: 'Token not found',
          ...errorSchema
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const { tokenAddress } = request.params;
    const { timeframe = '24h' } = request.query;

    if (!isValidTokenAddress(tokenAddress)) {
      return reply.code(400).send({ error: 'Invalid token address format' });
    }

    // Validate timeframe
    const validTimeframes = ['1h', '6h', '24h', '7d', '30d'];
    if (!validTimeframes.includes(timeframe)) {
      return reply.code(400).send({ error: 'Invalid timeframe. Must be one of: 1h, 6h, 24h, 7d, 30d' });
    }

    // Convert timeframe to hours for calculation
    const timeframeHours = {
      '1h': 1,
      '6h': 6,
      '24h': 24,
      '7d': 168,
      '30d': 720
    };

    const hours = timeframeHours[timeframe] || 24;

    try {
      const hypePrice = await getHypePrice();

      // Calculate timestamp for timeframe start
      const timeframeStart = Math.floor(Date.now() / 1000) - (hours * 3600);

      // Debug logging
      fastify.log.info(`Timeframe calculation: hours=${hours}, timeframeStart=${timeframeStart}, currentTime=${Math.floor(Date.now() / 1000)}`);

      const query = `
        WITH 
        timeframes AS (
          SELECT
            COUNT(CASE WHEN type = 'purchase' THEN 1 END) as buy_count,
            COUNT(CASE WHEN type = 'sale' THEN 1 END) as sell_count,
            COUNT(DISTINCT CASE WHEN type = 'purchase' THEN trader END) as unique_buyers,
            COUNT(DISTINCT CASE WHEN type = 'sale' THEN trader END) as unique_sellers,
            SUM(CASE WHEN type = 'purchase' THEN hype_amount::numeric ELSE 0 END) as buy_volume,
            SUM(CASE WHEN type = 'sale' THEN hype_amount::numeric ELSE 0 END) as sell_volume,
            SUM(hype_amount::numeric) as total_volume,
            -- Price change: compare latest price to oldest price in the specified timeframe
            (
              SELECT 
                CASE 
                  WHEN first_price.price > 0 THEN 
                    ((latest_price.price - first_price.price) / first_price.price) * 100
                  ELSE 0
                END
              FROM (
                SELECT price 
                FROM ${dbSchema}.swaps 
                WHERE token = $1 
                  AND timestamp >= $2
                ORDER BY timestamp ASC
                LIMIT 1
              ) first_price,
              (
                SELECT price 
                FROM ${dbSchema}.swaps 
                WHERE token = $1 
                ORDER BY timestamp DESC 
                LIMIT 1
              ) latest_price
            ) as price_change,
            -- Get first and last prices for the timeframe
            (
              SELECT price 
              FROM ${dbSchema}.swaps 
              WHERE token = $1 
                AND timestamp >= $2
              ORDER BY timestamp ASC
              LIMIT 1
            ) as first_price,
            (
              SELECT price 
              FROM ${dbSchema}.swaps 
              WHERE token = $1 
              ORDER BY timestamp DESC 
              LIMIT 1
            ) as last_price
          FROM ${dbSchema}.swaps
          WHERE token = $1 
            AND timestamp >= $2
        ),
        latest_metadata AS (
          SELECT 
            metadata
          FROM ${dbSchema}.token_metadata_history
          WHERE token = $1
          ORDER BY updated_at DESC
          LIMIT 1
        ),
        calculated_holders AS (
          -- Calculate estimated holder count from swap data as fallback
          -- This counts unique traders who have made purchases (rough estimate)
          SELECT 
            token,
            COUNT(DISTINCT trader) as calculated_holder_count
          FROM ${dbSchema}.swaps
          WHERE type = 'purchase'
          GROUP BY token
        ),
        last_swap AS (
          -- Get the most recent swap timestamp for this token
          SELECT 
            MAX(timestamp) as last_swap_timestamp
          FROM ${dbSchema}.swaps
          WHERE token = $1
        )
        SELECT 
          t.address,
          t.name,
          t.symbol,
          -- Use metadata from realtime_token_data table (most current), fallback to history, then tokens table
          COALESCE(rtd.metadata, lm.metadata, t.metadata) as metadata,
          t.creator,
          t.creation_timestamp,
          t.decimals,
          rtd.price_hype,
          rtd.market_cap_hype,
          rtd.hype_reserves,
          rtd.token_reserves,
          rtd.total_supply,
          rtd.bonded,
          rtd.frozen,
          -- Use calculated holder count if realtime data shows 0
          CASE 
            WHEN rtd.holder_count > 0 THEN rtd.holder_count
            ELSE COALESCE(ch.calculated_holder_count, 0)
          END as holder_count,
          tf.buy_count,
          tf.sell_count,
          tf.total_volume,
          tf.buy_volume,
          tf.sell_volume,
          tf.unique_buyers,
          tf.unique_sellers,
          tf.price_change,
          tf.first_price,
          tf.last_price,
          ls.last_swap_timestamp
        FROM ${dbSchema}.tokens t
        LEFT JOIN ${dbSchema}.realtime_token_data rtd ON t.address = rtd.token_address
        LEFT JOIN timeframes tf ON true
        LEFT JOIN latest_metadata lm ON true
        LEFT JOIN calculated_holders ch ON t.address = ch.token
        LEFT JOIN last_swap ls ON true
        WHERE t.address = $1
      `;

      const result = await pool.query(query, [tokenAddress, timeframeStart]);

      if (result.rows.length === 0) {
        return reply.code(404).send({ error: 'Token not found' });
      }

      const token = transformTokenData(result.rows[0], hypePrice);
      return token;

    } catch (error) {
      fastify.log.error('Error fetching token info. TokenAddress: %s, Timeframe: %s, Message: %s, Stack: %s, FullError: %j',
        tokenAddress, timeframe, error.message, error.stack, error);
      return reply.code(500).send({ error: 'Failed to fetch token info' });
    }
  });

  // Search tokens
  fastify.get('/api/tokens/search', {
    schema: {
      description: `Search tokens by name, symbol, or address.
      
      Real-time Data:
      - Search results include current token data
      - All financial metrics are calculated using real-time data`,
      tags: ['token'],
      summary: 'Search tokens with real-time data',
      querystring: searchQuery,
      response: {
        200: {
          description: 'Successful response',
          type: 'object',
          properties: {
            tokens: {
              type: 'array',
              items: searchTokenSchema
            }
          }
        },
        400: {
          description: 'Bad request',
          ...errorSchema
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const { query, limit = 10 } = request.query;

    if (!query || query.length < 1) {
      return reply.code(400).send({ error: 'Search query is required' });
    }

    if (limit < 1 || limit > 50) {
      return reply.code(400).send({ error: 'Limit must be between 1 and 50' });
    }

    try {
      let searchSql;
      let params;

      // If query looks like an address, search only by address
      if (isValidTokenAddress(query)) {
        searchSql = `
          SELECT address, name, symbol, decimals, metadata->>'image_uri' as image_uri
          FROM ${dbSchema}.tokens
          WHERE address = $1
          LIMIT 1
        `;
        params = [query];
      } else {
        // Otherwise search by name or symbol
        searchSql = `
          SELECT 
            address, 
            name,
            symbol,
            decimals,
            metadata->>'image_uri' as image_uri
          FROM ${dbSchema}.tokens
          WHERE 
            NOT (name = '' AND symbol = '')
            AND (
              LOWER(name) LIKE LOWER($1) OR
              LOWER(symbol) LIKE LOWER($1)
            )
          ORDER BY 
            CASE 
              WHEN LOWER(name) = LOWER($2) THEN 1
              WHEN LOWER(symbol) = LOWER($2) THEN 2
              WHEN LOWER(name) LIKE LOWER($2 || '%') THEN 3
              WHEN LOWER(symbol) LIKE LOWER($2 || '%') THEN 4
              ELSE 5
            END,
            name ASC
          LIMIT $3
        `;
        params = [`%${query}%`, query, limit];
      }

      const result = await pool.query(searchSql, params);
      return { tokens: result.rows };

    } catch (error) {
      fastify.log.error('Error searching tokens:', error);
      return reply.code(500).send({ error: 'Failed to search tokens' });
    }
  });

  // List tokens with filtering and sorting
  fastify.get('/api/tokens', {
    schema: {
      description: `List tokens with filtering and sorting options.
      
      Real-time Data:
      - Market cap, price, and volume calculations use real-time data
      - Holder counts are fetched from real-time tables
      - All financial metrics are calculated using current HYPE price
      - Trading statistics (volume, trades, price change) can be calculated for different timeframes (1h, 6h, 24h, 7d, 30d)`,
      tags: ['token'],
      summary: 'List tokens with real-time metrics and configurable timeframes',
      querystring: {
        allOf: [paginationQuery, tokenSortQuery, tokenFilterQuery, timeframeQuery]
      },
      response: {
        200: {
          description: 'Successful response',
          type: 'object',
          properties: {
            pagination: paginationSchema,
            tokens: {
              type: 'array',
              items: tokenSchema
            }
          }
        },
        400: {
          description: 'Bad request',
          ...errorSchema
        },
        500: {
          description: 'Server error',
          ...errorSchema
        }
      }
    }
  }, async (request, reply) => {
    const {
      page = 1,
      limit = 20,
      sort_by = 'latest_activity',
      sort_order = 'desc',
      min_mcap = '0',
      min_volume_24h = '0',
      min_holders = 0,
      min_swaps = 0,
      max_age_days = 365,
      bonding_status = 'all',
      is_bonded,
      timeframe = '24h'
    } = request.query;

    if (page < 1) {
      return reply.code(400).send({ error: 'Page number must be 1 or greater' });
    }
    if (limit < 1 || limit > 100) {
      return reply.code(400).send({ error: 'Limit must be between 1 and 100' });
    }

    // Validate timeframe
    const validTimeframes = ['1h', '6h', '24h', '7d', '30d'];
    if (!validTimeframes.includes(timeframe)) {
      return reply.code(400).send({ error: 'Invalid timeframe. Must be one of: 1h, 6h, 24h, 7d, 30d' });
    }

    // Convert timeframe to hours for calculation
    const timeframeHours = {
      '1h': 1,
      '6h': 6,
      '24h': 24,
      '7d': 168,
      '30d': 720
    };

    const hours = timeframeHours[timeframe] || 24;
    const offset = (page - 1) * limit;
    const timeframeStart = Math.floor(Date.now() / 1000) - (hours * 3600);

    // Build dynamic ORDER BY clause and determine what data we need
    let orderByClause = '';
    let needsSwapData = false;
    let needsActivityData = false;
    let needsVolumeData = false;
    let needsPriceChangeData = false;

    switch (sort_by) {
      case 'latest_activity':
        orderByClause = `ORDER BY COALESCE(ls.last_swap_timestamp, t.creation_timestamp) ${sort_order.toUpperCase()}`;
        break;
      case 'mcap':
        orderByClause = `ORDER BY COALESCE(rtd.market_cap_hype::numeric, 0) ${sort_order.toUpperCase()}`;
        break;
      case 'age':
        orderByClause = `ORDER BY t.creation_timestamp ${sort_order.toUpperCase()}`;
        break;
      case 'holders_count':
        orderByClause = `ORDER BY COALESCE(rtd.holder_count, ch.calculated_holder_count, 0) ${sort_order.toUpperCase()}`;
        break;
      case 'volume_24h':
        orderByClause = `ORDER BY COALESCE(tf.total_volume, 0) ${sort_order.toUpperCase()}`;
        break;
      case 'price_change_24h':
        orderByClause = `ORDER BY COALESCE(tf.price_change, 0) ${sort_order.toUpperCase()}`;
        break;
      case 'trades_24h':
        orderByClause = `ORDER BY (COALESCE(tf.buy_count, 0) + COALESCE(tf.sell_count, 0)) ${sort_order.toUpperCase()}`;
        break;
      default:
        orderByClause = `ORDER BY COALESCE(ls.last_swap_timestamp, t.creation_timestamp) DESC`;
    }

    // Build WHERE clause for filtering
    let whereClause = `
      WHERE t.creation_timestamp >= EXTRACT(EPOCH FROM NOW() - INTERVAL '${max_age_days} days')::integer
        AND NOT (t.name = '' AND t.symbol = '')
    `;

    // Add bonding status filter
    if (bonding_status !== 'all') {
      switch (bonding_status) {
        case 'bonded':
          whereClause += ` AND rtd.bonded = true`;
          break;
        case 'in_progress':
          whereClause += ` AND (rtd.bonded = false OR rtd.bonded IS NULL)`;
          break;
      }
    }

    // Add is_bonded filter (alternative to bonding_status)
    if (is_bonded !== undefined) {
      if (is_bonded === 'true' || is_bonded === true) {
        whereClause += ` AND rtd.bonded = true`;
      } else if (is_bonded === 'false' || is_bonded === false) {
        whereClause += ` AND (rtd.bonded = false OR rtd.bonded IS NULL)`;
      }
    }

    // Add other filters
    if (min_mcap !== '0') {
      whereClause += ` AND COALESCE(rtd.market_cap_hype::numeric, 0) >= ${min_mcap}`;
    }
    if (min_volume_24h !== '0') {
      whereClause += ` AND COALESCE(tf.total_volume, 0) >= ${min_volume_24h}`;
    }
    if (min_holders > 0) {
      whereClause += ` AND COALESCE(rtd.holder_count, ch.calculated_holder_count, 0) >= ${min_holders}`;
    }
    if (min_swaps > 0) {
      whereClause += ` AND (COALESCE(tf.buy_count, 0) + COALESCE(tf.sell_count, 0)) >= ${min_swaps}`;
    }

    // Get token trading stats for the timeframe
    const token_timeframes = `
      SELECT 
        s.token,
        COUNT(CASE WHEN s.type = 'purchase' THEN 1 END) as buy_count,
        COUNT(CASE WHEN s.type = 'sale' THEN 1 END) as sell_count,
        COUNT(DISTINCT CASE WHEN s.type = 'purchase' THEN s.trader END) as unique_buyers,
        COUNT(DISTINCT CASE WHEN s.type = 'sale' THEN s.trader END) as unique_sellers,
        SUM(CASE WHEN s.type = 'purchase' THEN s.hype_amount::numeric ELSE 0 END) as buy_volume,
        SUM(CASE WHEN s.type = 'sale' THEN s.hype_amount::numeric ELSE 0 END) as sell_volume,
        SUM(s.hype_amount::numeric) as total_volume,
        MIN(s.timestamp) as first_swap,
        MAX(s.timestamp) as last_swap,
        -- Price change calculation: compare first price in timeframe to latest overall price
        (
          SELECT 
            CASE 
              WHEN first_price.price > 0 THEN 
                ((latest_price.price - first_price.price) / first_price.price) * 100
              ELSE 0
            END
          FROM (
            SELECT price 
            FROM ${dbSchema}.swaps 
            WHERE token = s.token 
              AND timestamp >= $3
            ORDER BY timestamp ASC
            LIMIT 1
          ) first_price,
          (
            SELECT price 
            FROM ${dbSchema}.swaps 
            WHERE token = s.token 
            ORDER BY timestamp DESC 
            LIMIT 1
          ) latest_price
        ) as price_change,
        -- Get first and last prices for the timeframe
        (
          SELECT price 
          FROM ${dbSchema}.swaps 
          WHERE token = s.token 
            AND timestamp >= $3
          ORDER BY timestamp ASC
          LIMIT 1
        ) as first_price,
        (
          SELECT price 
          FROM ${dbSchema}.swaps 
          WHERE token = s.token 
          ORDER BY timestamp DESC 
          LIMIT 1
        ) as last_price
      FROM ${dbSchema}.swaps s
      WHERE s.timestamp >= $3
      GROUP BY s.token
    `;

    // Enhanced query with timeframe data and latest metadata
    const tokensQuery = `
      WITH token_timeframes AS (
        ${token_timeframes}
      ),
      latest_metadata AS (
        SELECT DISTINCT ON (tmh.token)
          tmh.token,
          tmh.metadata
        FROM ${dbSchema}.token_metadata_history tmh
        ORDER BY tmh.token, tmh.updated_at DESC
      ),
      calculated_holders AS (
        -- Calculate estimated holder count from swap data as fallback
        -- This counts unique traders who have made purchases (rough estimate)
        SELECT 
          token,
          COUNT(DISTINCT trader) as calculated_holder_count
        FROM ${dbSchema}.swaps
        WHERE type = 'purchase'
        GROUP BY token
      ),
      last_swaps AS (
        -- Get the most recent swap timestamp for each token
        SELECT 
          token,
          MAX(timestamp) as last_swap_timestamp
        FROM ${dbSchema}.swaps
        GROUP BY token
      )
      SELECT 
        t.address,
        t.decimals,
        t.name,
        t.symbol,
        -- Use metadata from realtime_token_data table (most current), fallback to history, then tokens table
        COALESCE(rtd.metadata, lm.metadata, t.metadata) as metadata,
        t.creator,
        t.creation_timestamp,
        rtd.price_hype,
        rtd.market_cap_hype,
        rtd.hype_reserves,
        rtd.token_reserves,
        rtd.total_supply,
        rtd.bonded,
        rtd.frozen,
        -- Use calculated holder count if realtime data shows 0
        CASE 
          WHEN rtd.holder_count > 0 THEN rtd.holder_count
          ELSE COALESCE(ch.calculated_holder_count, 0)
        END as holder_count,
        COALESCE(tf.buy_count, 0) as buy_count,
        COALESCE(tf.sell_count, 0) as sell_count,
        COALESCE(tf.unique_buyers, 0) as unique_buyers,
        COALESCE(tf.unique_sellers, 0) as unique_sellers,
        COALESCE(tf.buy_volume, 0) as buy_volume,
        COALESCE(tf.sell_volume, 0) as sell_volume,
        COALESCE(tf.total_volume, 0) as total_volume,
        COALESCE(tf.price_change, 0) as price_change,
        COALESCE(tf.first_price, 0) as first_price,
        COALESCE(tf.last_price, 0) as last_price,
        ls.last_swap_timestamp
      FROM ${dbSchema}.tokens t
      LEFT JOIN ${dbSchema}.realtime_token_data rtd ON t.address = rtd.token_address
      LEFT JOIN token_timeframes tf ON t.address = tf.token
      LEFT JOIN latest_metadata lm ON t.address = lm.token
      LEFT JOIN calculated_holders ch ON t.address = ch.token
      LEFT JOIN last_swaps ls ON t.address = ls.token
      ${whereClause}
      ${orderByClause}
      LIMIT $1 OFFSET $2
    `;

    const countQuery = `
      WITH calculated_holders AS (
        -- Calculate estimated holder count from swap data as fallback
        -- This counts unique traders who have made purchases (rough estimate)
        SELECT 
          token,
          COUNT(DISTINCT trader) as calculated_holder_count
        FROM ${dbSchema}.swaps
        WHERE type = 'purchase'
        GROUP BY token
      )
      SELECT COUNT(*) as count
      FROM ${dbSchema}.tokens t
      LEFT JOIN ${dbSchema}.realtime_token_data rtd ON t.address = rtd.token_address
      LEFT JOIN calculated_holders ch ON t.address = ch.token
      ${whereClause}
    `;

    try {
      const hypePrice = await getHypePrice();

      const [tokensResult, countResult] = await Promise.all([
        pool.query(tokensQuery, [limit, offset, timeframeStart]),
        pool.query(countQuery)
      ]);

      const totalTokens = parseInt(countResult.rows[0].count, 10);
      const totalPages = Math.ceil(totalTokens / limit);

      const tokens = tokensResult.rows.map(token => transformTokenData(token, hypePrice));

      return {
        pagination: {
          currentPage: page,
          totalPages,
          totalTokens,
          limit
        },
        tokens
      };

    } catch (error) {
      fastify.log.error('Error fetching tokens: %j', {
        error: error.message,
        stack: error.stack,
        query: tokensQuery,
        params: [limit, offset, timeframeStart],
        timeframe,
        sort_by,
        sort_order
      });
      return reply.code(500).send({ error: 'Failed to fetch tokens' });
    }
  });

  // Register token operations routes
  await fastify.register(require('./token-operations'));
}

module.exports = tokenRoutes; 