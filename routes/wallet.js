const fastifyPlugin = require('fastify-plugin');
const { 
  walletSwapsResponseSchema, 
  walletAddressParam, 
  walletSwapsQuery 
} = require('../schemas/wallet');
const { errorSchema } = require('../schemas/common');
const { isValidTokenAddress } = require('../utils/tokenHelpers');

async function walletRoutes(fastify, options) {
  const { pool } = fastify;
  const dbSchema = process.env.DB_SCHEMA || 'll_history_schema';

  // DEBUG: Add a test endpoint to check if data exists
  fastify.get('/api/wallet/:walletAddress/debug', {
    schema: {
      description: 'Debug endpoint to check wallet data existence',
      tags: ['wallet'],
      summary: 'Debug wallet data',
      params: walletAddressParam
    }
  }, async (request, reply) => {
    const { walletAddress } = request.params;
    
    try {
      // Test basic connection
      const connectionTest = await pool.query('SELECT 1 as test');
      
      // Check if any swaps exist at all
      const totalSwapsQuery = `SELECT COUNT(*) as total FROM ${dbSchema}.swaps`;
      const totalSwapsResult = await pool.query(totalSwapsQuery);
      
      // Check swaps for this specific wallet
      const walletSwapsQuery = `
        SELECT COUNT(*) as count, 
               ARRAY_AGG(DISTINCT trader) as traders_sample
        FROM ${dbSchema}.swaps 
        WHERE trader = $1
      `;
      const normalizedAddress = walletAddress.toLowerCase();
      const walletSwapsResult = await pool.query(walletSwapsQuery, [normalizedAddress]);
      
      // Check a few recent swaps to see format
      const recentSwapsQuery = `
        SELECT trader, token, type, hype_amount, timestamp 
        FROM ${dbSchema}.swaps 
        ORDER BY timestamp DESC 
        LIMIT 5
      `;
      const recentSwapsResult = await pool.query(recentSwapsQuery);
      
      return {
        connection: connectionTest.rows[0],
        dbSchema,
        walletAddress,
        totalSwapsInDb: totalSwapsResult.rows[0].total,
        walletSwapsCount: walletSwapsResult.rows[0].count,
        recentSwaps: recentSwapsResult.rows
      };
      
    } catch (error) {
      return reply.code(500).send({
        error: error.message,
        stack: error.stack
      });
    }
  });

  // Get wallet info
  fastify.get('/api/wallet/:walletAddress', {
    schema: {
      description: `Get wallet information including trading statistics.
      
      Features:
      - Total trading volume
      - Number of trades
      - Trading history
      - Portfolio overview`,
      tags: ['wallet'],
      summary: 'Get wallet information and trading stats',
      params: walletAddressParam,
      response: {
        200: {
          description: 'Successful response',
          type: 'object',
          properties: {
            address: { type: 'string' },
            total_volume: { type: 'string' },
            total_trades: { type: 'integer' },
            buy_count: { type: 'integer' },
            sell_count: { type: 'integer' },
            first_trade: { type: 'integer' },
            last_trade: { type: 'integer' },
            portfolio: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  token: { type: 'string' },
                  token_name: { type: 'string' },
                  token_symbol: { type: 'string' },
                  balance: { type: 'string' },
                  value_hype: { type: 'string' }
                }
              }
            }
          }
        },
        400: {
          description: 'Bad request',
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        500: {
          description: 'Server error',
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { walletAddress } = request.params;

    if (!isValidTokenAddress(walletAddress)) {
      return reply.code(400).send({ error: 'Invalid wallet address format' });
    }

    // Normalize address to lowercase to match database format and use indexes
    const normalizedAddress = walletAddress.toLowerCase();

    try {
      // Get wallet trading stats
      const statsQuery = `
        SELECT 
          COUNT(*) as total_trades,
          COUNT(CASE WHEN type = 'purchase' THEN 1 END) as buy_count,
          COUNT(CASE WHEN type = 'sale' THEN 1 END) as sell_count,
          SUM(hype_amount::numeric) as total_volume,
          MIN(timestamp) as first_trade,
          MAX(timestamp) as last_trade
        FROM ${dbSchema}.swaps
        WHERE trader = $1
      `;

      // Get portfolio overview
      const portfolioQuery = `
        WITH token_balances AS (
          SELECT 
            token,
            token_name,
            token_symbol,
            SUM(CASE 
              WHEN type = 'purchase' THEN token_amount::numeric
              WHEN type = 'sale' THEN -token_amount::numeric
              ELSE 0
            END) as balance
          FROM ${dbSchema}.swaps
          WHERE trader = $1
          GROUP BY token, token_name, token_symbol
          HAVING SUM(CASE 
            WHEN type = 'purchase' THEN token_amount::numeric
            WHEN type = 'sale' THEN -token_amount::numeric
            ELSE 0
          END) > 0
        )
        SELECT 
          tb.*,
          COALESCE((tb.balance * COALESCE(rtd.price_hype, 0)), 0)::text as value_hype
        FROM token_balances tb
        LEFT JOIN ${dbSchema}.realtime_token_data rtd ON tb.token = rtd.token_address
        ORDER BY (tb.balance * COALESCE(rtd.price_hype, 0)) DESC NULLS LAST
      `;

      const [statsResult, portfolioResult] = await Promise.all([
        pool.query(statsQuery, [normalizedAddress]),
        pool.query(portfolioQuery, [normalizedAddress])
      ]);

      const stats = statsResult.rows[0];
      const portfolio = portfolioResult.rows;

      return {
        address: walletAddress,
        total_volume: stats.total_volume || '0',
        total_trades: parseInt(stats.total_trades) || 0,
        buy_count: parseInt(stats.buy_count) || 0,
        sell_count: parseInt(stats.sell_count) || 0,
        first_trade: stats.first_trade || null,
        last_trade: stats.last_trade || null,
        portfolio
      };

    } catch (error) {
      fastify.log.error('Error fetching wallet info:', {
        error: error.message,
        stack: error.stack,
        wallet: walletAddress,
        dbSchema
      });
      return reply.code(500).send({ 
        error: 'Failed to fetch wallet info',
        details: error.message
      });
    }
  });

  // Get wallet swaps with optional token filter
  fastify.get('/api/wallet/:walletAddress/swaps', {
    schema: {
      description: `Get all swaps for a wallet with optional token filtering.
      
      Features:
      - Efficient querying using appropriate indexes
      - Optional token filtering
      - Pagination support
      - Sorting by timestamp
      - Includes token metadata and price data
      
      Performance:
      - Uses optimized indexes for wallet and token lookups
      - Efficient metadata caching
      - Parallel query execution
      - Minimal joins`,
      tags: ['wallet'],
      summary: 'Get wallet swaps with optional token filter',
      params: walletAddressParam,
      querystring: walletSwapsQuery,
      response: {
        200: {
          description: 'Successful response',
          type: 'object',
          properties: {
            pagination: {
              type: 'object',
              properties: {
                currentPage: { type: 'integer' },
                totalPages: { type: 'integer' },
                totalSwaps: { type: 'integer' },
                limit: { type: 'integer' }
              }
            },
            swaps: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  token: { type: 'string' },
                  type: { type: 'string', enum: ['purchase', 'sale'] },
                  hype_amount: { type: 'string' },
                  token_amount: { type: 'string' },
                  price: { type: 'string' },
                  timestamp: { type: 'integer' },
                  tx_hash: { type: 'string' },
                  token_name: { type: 'string' },
                  token_symbol: { type: 'string' },
                  metadata: { type: 'object' }
                }
              }
            }
          }
        },
        400: {
          description: 'Bad request',
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        500: {
          description: 'Server error',
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { walletAddress } = request.params;
    const { token, page = 1, limit = 20, sort_order = 'desc' } = request.query;

    if (!isValidTokenAddress(walletAddress)) {
      return reply.code(400).send({ error: 'Invalid wallet address format' });
    }

    if (token && !isValidTokenAddress(token)) {
      return reply.code(400).send({ error: 'Invalid token address format' });
    }

    // Normalize addresses to lowercase to match database format and use indexes
    const normalizedAddress = walletAddress.toLowerCase();
    const normalizedToken = token ? token.toLowerCase() : null;

    if (page < 1) {
      return reply.code(400).send({ error: 'Page number must be 1 or greater' });
    }

    if (limit < 1 || limit > 100) {
      return reply.code(400).send({ error: 'Limit must be between 1 and 100' });
    }

    const offset = (page - 1) * limit;
    const orderBy = sort_order.toUpperCase();

    try {
      // Build the base query with appropriate indexes
      let query = `
        WITH latest_metadata AS (
          SELECT DISTINCT ON (token)
            token,
            metadata
          FROM ${dbSchema}.token_metadata_history
          ORDER BY token, updated_at DESC
        )
        SELECT 
          s.token,
          s.type,
          s.hype_amount::text,
          s.token_amount::text,
          s.price::text,
          s.timestamp,
          s.tx_hash,
          s.token_name,
          s.token_symbol,
          COALESCE(lm.metadata, '{}'::jsonb) as metadata
        FROM ${dbSchema}.swaps s
        LEFT JOIN latest_metadata lm ON s.token = lm.token
        WHERE s.trader = $1
      `;

      // Add token filter if provided
      const params = [normalizedAddress];
      if (normalizedToken) {
        query += ` AND s.token = $2`;
        params.push(normalizedToken);
      }

      // Add ordering and pagination
      query += ` ORDER BY s.timestamp ${orderBy} LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
      params.push(limit, offset);

      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) as count
        FROM ${dbSchema}.swaps
        WHERE trader = $1
        ${normalizedToken ? 'AND token = $2' : ''}
      `;

      const [swapsResult, countResult] = await Promise.all([
        pool.query(query, params),
        pool.query(countQuery, normalizedToken ? [normalizedAddress, normalizedToken] : [normalizedAddress])
      ]);

      const totalSwaps = parseInt(countResult.rows[0].count, 10);
      const totalPages = Math.ceil(totalSwaps / limit);

      return {
        pagination: {
          currentPage: page,
          totalPages,
          totalSwaps,
          limit
        },
        swaps: swapsResult.rows
      };

    } catch (error) {
      fastify.log.error('Error fetching wallet swaps:', error);
      return reply.code(500).send({ error: 'Failed to fetch wallet swaps' });
    }
  });
}

module.exports = fastifyPlugin(walletRoutes); 