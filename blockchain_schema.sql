--
-- PostgreSQL database dump
--

-- Dumped from database version 14.18 (Ubuntu 14.18-0ubuntu0.22.04.1)
-- Dumped by pg_dump version 14.18 (Ubuntu 14.18-0ubuntu0.22.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: blocks; Type: TABLE; Schema: public; Owner: liquid_indexer
--

CREATE TABLE public.blocks (
    number bigint NOT NULL,
    hash character varying(66) NOT NULL,
    "timestamp" timestamp without time zone NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.blocks OWNER TO liquid_indexer;

--
-- Name: logs; Type: TABLE; Schema: public; Owner: liquid_indexer
--

CREATE TABLE public.logs (
    id integer NOT NULL,
    transaction_hash character varying(66) NOT NULL,
    log_index integer NOT NULL,
    address character varying(42) NOT NULL,
    topics jsonb NOT NULL,
    data text,
    removed boolean NOT NULL,
    block_number bigint NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.logs OWNER TO liquid_indexer;

--
-- Name: logs_id_seq; Type: SEQUENCE; Schema: public; Owner: liquid_indexer
--

CREATE SEQUENCE public.logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.logs_id_seq OWNER TO liquid_indexer;

--
-- Name: logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: liquid_indexer
--

ALTER SEQUENCE public.logs_id_seq OWNED BY public.logs.id;


--
-- Name: receipts; Type: TABLE; Schema: public; Owner: liquid_indexer
--

CREATE TABLE public.receipts (
    transaction_hash character varying(66) NOT NULL,
    status bigint NOT NULL,
    cumulative_gas_used bigint NOT NULL,
    gas_used bigint NOT NULL,
    contract_address character varying(42),
    logs_bloom text NOT NULL,
    block_number bigint NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.receipts OWNER TO liquid_indexer;

--
-- Name: transactions; Type: TABLE; Schema: public; Owner: liquid_indexer
--

CREATE TABLE public.transactions (
    hash character varying(66) NOT NULL,
    block_number bigint NOT NULL,
    block_hash character varying(66) NOT NULL,
    transaction_index integer NOT NULL,
    from_address character varying(42) NOT NULL,
    to_address character varying(42),
    value text NOT NULL,
    gas bigint NOT NULL,
    gas_price text NOT NULL,
    nonce bigint NOT NULL,
    input_data text,
    "timestamp" timestamp without time zone NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.transactions OWNER TO liquid_indexer;

--
-- Name: logs id; Type: DEFAULT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.logs ALTER COLUMN id SET DEFAULT nextval('public.logs_id_seq'::regclass);


--
-- Name: blocks blocks_pkey; Type: CONSTRAINT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.blocks
    ADD CONSTRAINT blocks_pkey PRIMARY KEY (number);


--
-- Name: logs logs_pkey; Type: CONSTRAINT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.logs
    ADD CONSTRAINT logs_pkey PRIMARY KEY (id);


--
-- Name: logs logs_unique; Type: CONSTRAINT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.logs
    ADD CONSTRAINT logs_unique UNIQUE (transaction_hash, log_index);


--
-- Name: receipts receipts_pkey; Type: CONSTRAINT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.receipts
    ADD CONSTRAINT receipts_pkey PRIMARY KEY (transaction_hash);


--
-- Name: transactions transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_pkey PRIMARY KEY (hash);


--
-- Name: idx_blocks_timestamp; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_blocks_timestamp ON public.blocks USING btree ("timestamp");


--
-- Name: idx_logs_address; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_logs_address ON public.logs USING btree (address);


--
-- Name: idx_logs_block_number; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_logs_block_number ON public.logs USING btree (block_number);


--
-- Name: idx_logs_topics; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_logs_topics ON public.logs USING gin (topics jsonb_path_ops);


--
-- Name: idx_logs_tx_hash_log_index; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_logs_tx_hash_log_index ON public.logs USING btree (transaction_hash, log_index);


--
-- Name: idx_receipts_block_number; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_receipts_block_number ON public.receipts USING btree (block_number);


--
-- Name: idx_receipts_contract_address; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_receipts_contract_address ON public.receipts USING btree (contract_address) WHERE (contract_address IS NOT NULL);


--
-- Name: idx_transactions_block_number; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_transactions_block_number ON public.transactions USING btree (block_number);


--
-- Name: idx_transactions_from_address; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_transactions_from_address ON public.transactions USING btree (from_address);


--
-- Name: idx_transactions_input_data_prefix; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_transactions_input_data_prefix ON public.transactions USING btree (SUBSTRING(input_data FROM 1 FOR 256));


--
-- Name: idx_transactions_timestamp; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_transactions_timestamp ON public.transactions USING btree ("timestamp");


--
-- Name: idx_transactions_to_address; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_transactions_to_address ON public.transactions USING btree (to_address) WHERE (to_address IS NOT NULL);


--
-- Name: idx_transactions_value; Type: INDEX; Schema: public; Owner: liquid_indexer
--

CREATE INDEX idx_transactions_value ON public.transactions USING btree (value);


--
-- Name: logs logs_transaction_hash_fkey; Type: FK CONSTRAINT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.logs
    ADD CONSTRAINT logs_transaction_hash_fkey FOREIGN KEY (transaction_hash) REFERENCES public.transactions(hash) ON DELETE CASCADE;


--
-- Name: receipts receipts_transaction_hash_fkey; Type: FK CONSTRAINT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.receipts
    ADD CONSTRAINT receipts_transaction_hash_fkey FOREIGN KEY (transaction_hash) REFERENCES public.transactions(hash) ON DELETE CASCADE;


--
-- Name: transactions transactions_block_number_fkey; Type: FK CONSTRAINT; Schema: public; Owner: liquid_indexer
--

ALTER TABLE ONLY public.transactions
    ADD CONSTRAINT transactions_block_number_fkey FOREIGN KEY (block_number) REFERENCES public.blocks(number) ON DELETE CASCADE;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

GRANT ALL ON SCHEMA public TO liquid_indexer;
GRANT USAGE ON SCHEMA public TO indexer_readonly;


--
-- Name: TABLE blocks; Type: ACL; Schema: public; Owner: liquid_indexer
--

GRANT SELECT ON TABLE public.blocks TO indexer_readonly;
GRANT SELECT ON TABLE public.blocks TO llindex;


--
-- Name: TABLE logs; Type: ACL; Schema: public; Owner: liquid_indexer
--

GRANT SELECT ON TABLE public.logs TO indexer_readonly;
GRANT SELECT ON TABLE public.logs TO llindex;


--
-- Name: TABLE receipts; Type: ACL; Schema: public; Owner: liquid_indexer
--

GRANT SELECT ON TABLE public.receipts TO indexer_readonly;
GRANT SELECT ON TABLE public.receipts TO llindex;


--
-- Name: TABLE transactions; Type: ACL; Schema: public; Owner: liquid_indexer
--

GRANT SELECT ON TABLE public.transactions TO indexer_readonly;
GRANT SELECT ON TABLE public.transactions TO llindex;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES  TO liquid_indexer;


--
-- Name: DEFAULT PRIVILEGES FOR FUNCTIONS; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON FUNCTIONS  TO liquid_indexer;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES  TO liquid_indexer;
ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT SELECT ON TABLES  TO indexer_readonly;


--
-- PostgreSQL database dump complete
--

