// server-new.js
const fastify = require('fastify')({
  logger: process.env.NODE_ENV === 'development' ? {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true
      }
    }
  } : { level: 'info' }
});

require('dotenv').config();

// Error handler
fastify.setErrorHandler((error, request, reply) => {
  fastify.log.error(error);

  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal Server Error';

  reply.status(statusCode).send({
    error: message,
    statusCode
  });
});

// Security and performance plugins
async function setupServer() {
  try {
    // CORS support
    await fastify.register(require('@fastify/cors'), {
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST'],
      credentials: true
    });

    // Socket.IO support
    await fastify.register(require('fastify-socket.io'), {
      cors: {
        origin: process.env.CORS_ORIGIN || '*',
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });


    // Helmet for security - Configure CSP to allow Swagger UI
    await fastify.register(require('@fastify/helmet'), {
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "validator.swagger.io"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"]
        }
      }
    });

    // Compression
    await fastify.register(require('@fastify/compress'), {
      global: true
    });

    // Swagger documentation
    await fastify.register(require('@fastify/swagger'), {
      openapi: {
        openapi: '3.0.0',
        info: {
          title: 'OHLCV WebSocket API',
          description: `API documentation for the OHLCV WebSocket server.
          
          Token Bonding Information:
          - Initial HYPE: 300 HYPE
          - Total Supply: 1B tokens
          - Bonding Threshold: 90% tokens sold
          - Tokens Left at 100%: 100M tokens
          - HYPE Needed for 100%: 30 HYPE (10% of initial)
          
          Real-time Data:
          - Market cap, price, holder count, and supply data are fetched from real-time tables
          - All financial calculations use real-time data for accuracy
          - Bonding progress is calculated based on current token reserves and HYPE in pool`,
          version: '2.0.0',
          contact: {
            name: 'API Support',
            email: '<EMAIL>'
          }
        },
        servers: [
          {
            url: 'https://donkey-api.liquidlaunch.app',
            description: 'Production server'
          },
          {
            url: 'http://localhost:3050',
            description: 'Development server'
          }
        ],
        tags: [
          {
            name: 'token',
            description: 'Token-related endpoints including bonding progress and real-time data'
          },
          {
            name: 'ohlcv',
            description: 'OHLCV data endpoints for wallet tracking'
          },
          {
            name: 'wallet',
            description: 'Wallet-related endpoints for tracking swaps and transactions'
          }
        ],
        components: {
          securitySchemes: {
            apiKey: {
              type: 'apiKey',
              name: 'X-API-Key',
              in: 'header'
            }
          }
        }
      }
    });

    // Swagger UI - Updated configuration
    await fastify.register(require('@fastify/swagger-ui'), {
      routePrefix: '/documentation',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
        defaultModelsExpandDepth: 2,
        defaultModelExpandDepth: 2
      },
      staticCSP: false, // Disable static CSP since we're handling it globally
      transformStaticCSP: (header) => header
    });

    // Register plugins
    await fastify.register(require('./plugins/database'));
    await fastify.register(require('./plugins/websocket'));

    // Register routes
    await Promise.all([
      fastify.register(require('./routes/tokens-clean')),
      fastify.register(require('./routes/ohlcv')),
      fastify.register(require('./routes/swaps')),
      fastify.register(require('./routes/wallet'))
    ]);

    // Health check endpoint
    fastify.get('/health', {
      schema: {
        description: 'Health check endpoint',
        tags: ['system'],
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
              uptime: { type: 'number' },
              version: { type: 'string' }
            }
          }
        }
      }
    }, async (request, reply) => {
      try {
        // Test database connection
        await fastify.pool.query('SELECT 1');

        return {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          version: process.env.npm_package_version || '2.0.0'
        };
      } catch (error) {
        reply.code(503);
        return {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          version: process.env.npm_package_version || '2.0.0',
          error: error.message
        };
      }
    });

    // 404 handler
    fastify.setNotFoundHandler((request, reply) => {
      reply.code(404).send({
        error: 'Route not found',
        statusCode: 404,
        message: `Route ${request.method} ${request.url} not found`
      });
    });

    // Start the server
    const port = process.env.PORT || 3050;
    const host = process.env.HOST || '0.0.0.0';

    await fastify.listen({ port, host });

    fastify.log.info(`🚀 Server listening on http://${host}:${port}`);
    fastify.log.info(`📚 Documentation available at http://${host}:${port}/documentation`);
    fastify.log.info(`🔧 Health check available at http://${host}:${port}/health`);

  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
}

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  fastify.log.info(`Received ${signal}, shutting down gracefully...`);

  try {
    await fastify.close();
    process.exit(0);
  } catch (err) {
    fastify.log.error('Error during shutdown:', err);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  fastify.log.fatal('Uncaught exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  fastify.log.fatal('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
setupServer(); 