const { Pool } = require('pg');

const pool = new Pool({
  host: 'localhost',
  database: 'liquidlaunch_history_prod',
  user: process.env.USER || 'derek'
});

async function testHolderCount() {
  try {
    const result = await pool.query(`
      WITH calculated_holders AS (
        SELECT 
          COUNT(DISTINCT trader) as calculated_holder_count
        FROM ll_history_schema.swaps
        WHERE token = '0x302f64fa6dbe1551bc683771289ecaf02ec15bb4' AND type = 'purchase'
      )
      SELECT 
        rtd.holder_count as realtime_holder_count,
        ch.calculated_holder_count,
        CASE 
          WHEN rtd.holder_count > 0 THEN rtd.holder_count
          ELSE COALESCE(ch.calculated_holder_count, 0)
        END as final_holder_count
      FROM ll_history_schema.realtime_token_data rtd
      CROSS JOIN calculated_holders ch
      WHERE rtd.token_address = '0x302f64fa6dbe1551bc683771289ecaf02ec15bb4'
    `);

    console.log('Holder count test result:');
    console.log(JSON.stringify(result.rows[0], null, 2));
    await pool.end();
  } catch (error) {
    console.error('Error:', error.message);
    await pool.end();
  }
}

testHolderCount(); 