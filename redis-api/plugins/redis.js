const fp = require('fastify-plugin');
const Redis = require('ioredis');

async function redisPlugin(fastify, options) {
  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD,
    retryStrategy: (times) => {
      const delay = Math.min(times * 50, 2000);
      return delay;
    }
  });

  // Helper methods for accessing data
  const helpers = {
    async getTokenStats(tokenAddress) {
      const key = `token_stats_${tokenAddress.toLowerCase()}`;
      const data = await redis.get(key);
      return data ? JSON.parse(data) : null;
    },

    async getSwaps(tokenAddress) {
      const key = `swaps_${tokenAddress.toLowerCase()}`;
      const data = await redis.get(key);
      return data ? JSON.parse(data) : null;
    },

    async getHolders(tokenAddress) {
      const key = `holders_${tokenAddress.toLowerCase()}`;
      const data = await redis.get(key);
      return data ? JSON.parse(data) : null;
    },

    async getOHLCV(tokenAddress, timeframe) {
      const key = `ohlcv_${tokenAddress.toLowerCase()}_${timeframe}`;
      const data = await redis.get(key);
      return data ? JSON.parse(data) : null;
    }
  };

  // Decorate fastify with redis instance and helpers
  fastify.decorate('redis', redis);
  fastify.decorate('redisHelpers', helpers);

  // Close Redis connection when server closes
  fastify.addHook('onClose', async () => {
    await redis.quit();
  });
}

module.exports = fp(redisPlugin, {
  name: 'redis-plugin',
  dependencies: []
}); 