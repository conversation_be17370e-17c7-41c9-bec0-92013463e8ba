const fastify = require('fastify')({
  logger: process.env.NODE_ENV === 'development' ? {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true
      }
    }
  } : { level: 'info' }
});

require('dotenv').config();

// Error handler
fastify.setErrorHandler((error, request, reply) => {
  fastify.log.error(error);

  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal Server Error';

  reply.status(statusCode).send({
    error: message,
    statusCode
  });
});

// Security and performance plugins
async function setupServer() {
  try {
    // CORS support
    await fastify.register(require('@fastify/cors'), {
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST'],
      credentials: true
    });

    // Socket.IO support
    await fastify.register(require('fastify-socket.io'), {
      cors: {
        origin: process.env.CORS_ORIGIN || '*',
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Helmet for security
    await fastify.register(require('@fastify/helmet'), {
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "validator.swagger.io"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"]
        }
      }
    });

    // Compression
    await fastify.register(require('@fastify/compress'), {
      global: true
    });

    // Swagger documentation
    await fastify.register(require('@fastify/swagger'), {
      openapi: {
        openapi: '3.0.0',
        info: {
          title: 'OHLCV WebSocket API (Redis)',
          description: `API documentation for the OHLCV WebSocket server with Redis backend.
          
          This API uses Redis as its data store, with the following key patterns:
          - token_stats_{token_address}: Token statistics and financial data
          - swaps_{token_address}: Swap history and data
          - holders_{token_address}: Holder information
          
          Data is updated in real-time via ZeroMQ messages processed by a separate service.`,
          version: '2.0.0',
          contact: {
            name: 'API Support',
            email: '<EMAIL>'
          }
        },
        servers: [
          {
            url: 'https://redis-api.liquidlaunch.app',
            description: 'Production server'
          },
          {
            url: 'http://localhost:3051',
            description: 'Development server'
          }
        ],
        tags: [
          {
            name: 'token',
            description: 'Token-related endpoints including bonding progress and real-time data'
          },
          {
            name: 'ohlcv',
            description: 'OHLCV data endpoints for wallet tracking'
          }
        ],
        components: {
          securitySchemes: {
            apiKey: {
              type: 'apiKey',
              name: 'X-API-Key',
              in: 'header'
            }
          }
        }
      }
    });

    // Swagger UI
    await fastify.register(require('@fastify/swagger-ui'), {
      routePrefix: '/documentation',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
        defaultModelsExpandDepth: 2,
        defaultModelExpandDepth: 2
      },
      staticCSP: false,
      transformStaticCSP: (header) => header
    });

    // Register plugins
    await fastify.register(require('./plugins/redis'));
    await fastify.register(require('./plugins/websocket'));

    // Register routes
    await fastify.register(require('./routes/tokens'));
    await fastify.register(require('./routes/ohlcv'));
    await fastify.register(require('./routes/swaps'));

    // Health check endpoint
    fastify.get('/health', {
      schema: {
        description: 'Health check endpoint',
        tags: ['system'],
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
              uptime: { type: 'number' },
              version: { type: 'string' },
              redis: { type: 'boolean' }
            }
          }
        }
      }
    }, async (request, reply) => {
      try {
        // Test Redis connection
        await fastify.redis.ping();

        return {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          version: process.env.npm_package_version || '2.0.0',
          redis: true
        };
      } catch (error) {
        reply.code(503);
        return {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          version: process.env.npm_package_version || '2.0.0',
          redis: false,
          error: error.message
        };
      }
    });

    // 404 handler
    fastify.setNotFoundHandler((request, reply) => {
      reply.code(404).send({
        error: 'Route not found',
        statusCode: 404,
        message: `Route ${request.method} ${request.url} not found`
      });
    });

    // Start the server
    const port = process.env.PORT || 3051;
    const host = process.env.HOST || '0.0.0.0';

    await fastify.listen({ port, host });

    fastify.log.info(`🚀 Redis API Server listening on http://${host}:${port}`);
    fastify.log.info(`📚 Documentation available at http://${host}:${port}/documentation`);
    fastify.log.info(`🔧 Health check available at http://${host}:${port}/health`);

  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
}

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  fastify.log.info(`Received ${signal}, shutting down gracefully...`);

  try {
    await fastify.close();
    process.exit(0);
  } catch (err) {
    fastify.log.error('Error during shutdown:', err);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  fastify.log.fatal('Uncaught exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  fastify.log.fatal('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
setupServer(); 