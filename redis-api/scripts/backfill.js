require('dotenv').config();
const Redis = require('ioredis');
const pino = require('pino');
const { Pool } = require('pg');
const { ethers } = require('ethers');

// Initialize logger
const logger = pino({
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true
    }
  }
});

// Initialize Redis client
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  username: process.env.REDIS_USER || 'default',
  password: process.env.REDIS_PASSWORD,
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  }
});

// Initialize PostgreSQL connection
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD
});

// Contract ABI
const factoryABI = [
  "function createToken(string name, string symbol, string image_uri, string description, string website, string twitter, string telegram, string discord) external payable returns (address)",
  "function buyTokens(address token) external payable",
  "function sellTokens(address token, uint256 tokenAmount) external",
  "function getTokenCount() external view returns (uint256)",
  "function getPaginatedTokensWithMetadata(uint256 start, uint256 limit) external view returns (address[], tuple(string name, string symbol, string image_uri, string description, string website, string twitter, string telegram, string discord, address creator, uint256 creationTimestamp, uint256 lastUpdated, uint256 startingLiquidity, uint256 currentHypeReserves, uint256 currentTokenReserves, uint256 totalSupply, uint256 currentPrice)[])",
  "event TokenCreated(address indexed token, address indexed creator, string name, string symbol, string image_uri, string description, string website, string twitter, string telegram, string discord, uint256 creationTimestamp, uint256 startingLiquidity, uint256 currentHypeReserves, uint256 currentTokenReserves, uint256 totalSupply, uint256 currentPrice, uint256 initialPurchaseAmount)",
  "event TokensPurchased(address indexed token, address indexed buyer, uint256 hypeIn, uint256 tokensOut, uint256 price, uint256 timestamp, uint256 hypeReserves, uint256 tokenReserves, uint256 totalSupply, string name, string symbol)",
  "event TokensSold(address indexed token, address indexed seller, uint256 tokensIn, uint256 hypeOut, uint256 price, uint256 timestamp, uint256 hypeReserves, uint256 tokenReserves, uint256 totalSupply, string name, string symbol)",
  "event TokenMetadataUpdated(address indexed token, address indexed creator, string name, string symbol, string image_uri, string description, string website, string twitter, string telegram, string discord, uint256 creationTimestamp, uint256 lastUpdated, uint256 startingLiquidity, uint256 currentHypeReserves, uint256 currentTokenReserves, uint256 totalSupply, uint256 currentPrice)",
  "event PairCreated(address token, address pair)",
  "event LiquidityBurned(address indexed token, uint256 amount)",
  "event TokensBurned(address indexed token, uint256 amount)",
  "event TokenBonded(address indexed token)",
  "event TokenFrozen(address indexed token)",
  "event Transfer(address indexed from, address indexed to, uint256 value)"
];

// Event signatures
const TOKENS_PURCHASED_EVENT = 'TokensPurchased(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,string,string)';
const TOKENS_SOLD_EVENT = 'TokensSold(address,address,uint256,uint256,uint256,uint256,uint256,uint256,uint256,string,string)';
const TOKEN_CREATED_EVENT = 'TokenCreated(address,address,string,string,string,string,string,string,string,string,uint256,uint256,uint256,uint256,uint256,uint256,uint256)';

// Initialize provider
const provider = new ethers.JsonRpcProvider(process.env.RPC_URL || 'http://localhost:8545');

async function getTokenInfo(tokenAddress) {
  try {
    // Get token creation event
    const creationQuery = `
      SELECT 
        l.transaction_hash,
        l.log_index,
        l.topics,
        l.data,
        t.timestamp,
        t.block_number
      FROM logs l
      JOIN transactions t ON l.transaction_hash = t.hash
      WHERE l.topics->0 = $1
        AND l.data LIKE $2
      ORDER BY t.block_number ASC
      LIMIT 1
    `;
    
    const creationEventSignature = ethers.id(TOKEN_CREATED_EVENT);
    const creationResult = await pool.query(creationQuery, [
      creationEventSignature,
      `%${tokenAddress.slice(2).toLowerCase()}%`
    ]);

    if (creationResult.rows.length === 0) {
      throw new Error('Token creation event not found');
    }

    // Get token transfers and swap events
    const eventsQuery = `
      SELECT 
        l.transaction_hash,
        l.log_index,
        l.topics,
        l.data,
        t.timestamp,
        t.block_number
      FROM logs l
      JOIN transactions t ON l.transaction_hash = t.hash
      WHERE (
        (l.topics->0 = $1 AND l.data LIKE $2) OR
        (l.topics->0 = $3 AND l.data LIKE $2) OR
        (l.topics->0 = $4 AND l.data LIKE $2)
      )
      ORDER BY t.block_number DESC
      LIMIT 1000
    `;
    
    const purchaseEventSignature = ethers.id(TOKENS_PURCHASED_EVENT);
    const sellEventSignature = ethers.id(TOKENS_SOLD_EVENT);
    const transferEventSignature = ethers.id('Transfer(address,address,uint256)');
    
    const eventsResult = await pool.query(eventsQuery, [
      purchaseEventSignature,
      `%${tokenAddress.slice(2).toLowerCase()}%`,
      sellEventSignature,
      transferEventSignature
    ]);
    
    // Process events into swaps and holder data
    const swaps = [];
    const holders = new Map();
    let totalSupply = ethers.BigNumber.from(0);
    let currentPrice = 0;
    let currentHypeReserves = ethers.BigNumber.from(0);
    let currentTokenReserves = ethers.BigNumber.from(0);

    for (const event of eventsResult.rows) {
      const topics = event.topics;
      const eventSignature = topics[0];

      if (eventSignature === purchaseEventSignature) {
        // Process purchase event
        const decodedData = ethers.AbiCoder.defaultAbiCoder().decode(
          ['uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'string', 'string'],
          event.data
        );
        
        const [hypeIn, tokensOut, price, , , hypeReserves, tokenReserves, name, symbol] = decodedData;
        
        swaps.push({
          txHash: event.transaction_hash,
          timestamp: event.timestamp,
          amount: tokensOut.toString(),
          price: price.toString(),
          type: 'buy',
          hypeIn: hypeIn.toString(),
          hypeReserves: hypeReserves.toString(),
          tokenReserves: tokenReserves.toString()
        });

        currentPrice = Number(price);
        currentHypeReserves = hypeReserves;
        currentTokenReserves = tokenReserves;
      } else if (eventSignature === sellEventSignature) {
        // Process sell event
        const decodedData = ethers.AbiCoder.defaultAbiCoder().decode(
          ['uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'string', 'string'],
          event.data
        );
        
        const [tokensIn, hypeOut, price, , , hypeReserves, tokenReserves, name, symbol] = decodedData;
        
        swaps.push({
          txHash: event.transaction_hash,
          timestamp: event.timestamp,
          amount: tokensIn.toString(),
          price: price.toString(),
          type: 'sell',
          hypeOut: hypeOut.toString(),
          hypeReserves: hypeReserves.toString(),
          tokenReserves: tokenReserves.toString()
        });

        currentPrice = Number(price);
        currentHypeReserves = hypeReserves;
        currentTokenReserves = tokenReserves;
      } else if (eventSignature === transferEventSignature) {
        // Process transfer event
        const from = '0x' + topics[1].slice(26);
        const to = '0x' + topics[2].slice(26);
        const amount = ethers.BigNumber.from(event.data);

        if (from !== '******************************************') {
          const fromBalance = holders.get(from) || ethers.BigNumber.from(0);
          holders.set(from, fromBalance.sub(amount));
        }
        
        if (to !== '******************************************') {
          const toBalance = holders.get(to) || ethers.BigNumber.from(0);
          holders.set(to, toBalance.add(amount));
        }

        totalSupply = totalSupply.add(amount);
      }
    }

    // Convert holders map to array and calculate percentages
    const holdersArray = Array.from(holders.entries())
      .map(([address, balance]) => ({
        address,
        balance: balance.toString(),
        percentage: (balance.mul(10000).div(totalSupply).toNumber() / 100).toFixed(2)
      }))
      .sort((a, b) => ethers.BigNumber.from(b.balance).sub(ethers.BigNumber.from(a.balance)).toNumber());

    // Calculate OHLCV data
    const ohlcvData = calculateOHLCV(swaps);

    // Get token metadata from creation event
    const creationData = ethers.AbiCoder.defaultAbiCoder().decode(
      ['string', 'string', 'string', 'string', 'string', 'string', 'string', 'string', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'uint256'],
      creationResult.rows[0].data
    );

    const [name, symbol, imageUri, description, website, twitter, telegram, discord] = creationData;

    return {
      tokenStats: {
        address: tokenAddress,
        name,
        symbol,
        imageUri,
        description,
        website,
        twitter,
        telegram,
        discord,
        price: currentPrice,
        marketCap: currentPrice * Number(totalSupply),
        volume24h: calculate24hVolume(swaps),
        holders: holdersArray.length,
        totalSupply: totalSupply.toString(),
        hypeReserves: currentHypeReserves.toString(),
        tokenReserves: currentTokenReserves.toString(),
        lastUpdated: new Date().toISOString()
      },
      swaps,
      holders: holdersArray,
      ohlcv: ohlcvData
    };
  } catch (error) {
    logger.error(`Error getting token info for ${tokenAddress}:`, error);
    throw error;
  }
}

async function backfillTokenData(tokenAddress) {
  try {
    const data = await getTokenInfo(tokenAddress);
    
    // Store token stats
    const statsKey = `token_stats_${tokenAddress.toLowerCase()}`;
    await redis.set(statsKey, JSON.stringify(data.tokenStats));
    
    // Store swaps
    const swapsKey = `swaps_${tokenAddress.toLowerCase()}`;
    await redis.set(swapsKey, JSON.stringify(data.swaps));
    
    // Store holders
    const holdersKey = `holders_${tokenAddress.toLowerCase()}`;
    await redis.set(holdersKey, JSON.stringify(data.holders));
    
    // Store OHLCV data for each timeframe
    for (const [timeframe, ohlcv] of Object.entries(data.ohlcv)) {
      const ohlcvKey = `ohlcv_${tokenAddress.toLowerCase()}_${timeframe}`;
      await redis.set(ohlcvKey, JSON.stringify(ohlcv));
    }
    
    logger.info(`Successfully backfilled data for token ${tokenAddress}`);
  } catch (error) {
    logger.error(`Error backfilling data for token ${tokenAddress}:`, error);
    throw error;
  }
}

function calculate24hVolume(swaps) {
  const oneDayAgo = Date.now() - 24 * 60 * 60 * 1000;
  return swaps
    .filter(swap => new Date(swap.timestamp).getTime() > oneDayAgo)
    .reduce((volume, swap) => {
      if (swap.type === 'buy') {
        return volume + Number(swap.hypeIn);
      } else {
        return volume + Number(swap.hypeOut);
      }
    }, 0);
}

function calculateOHLCV(swaps) {
  const timeframes = {
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '4h': 4 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000
  };

  const result = {};
  
  for (const [timeframe, interval] of Object.entries(timeframes)) {
    const candles = new Map();
    
    for (const swap of swaps) {
      const timestamp = new Date(swap.timestamp).getTime();
      const candleTime = Math.floor(timestamp / interval) * interval;
      const price = Number(swap.price);
      
      if (!candles.has(candleTime)) {
        candles.set(candleTime, {
          timestamp: candleTime,
          open: price,
          high: price,
          low: price,
          close: price,
          volume: Number(swap.amount)
        });
      } else {
        const candle = candles.get(candleTime);
        candle.high = Math.max(candle.high, price);
        candle.low = Math.min(candle.low, price);
        candle.close = price;
        candle.volume += Number(swap.amount);
      }
    }
    
    result[timeframe] = Array.from(candles.values())
      .sort((a, b) => a.timestamp - b.timestamp);
  }
  
  return result;
}

// Command line argument handling
const tokenAddress = process.argv[2];
if (!tokenAddress) {
  logger.error('Please provide a token address as an argument');
  process.exit(1);
}

// Run backfill
backfillTokenData(tokenAddress)
  .then(() => {
    logger.info('Backfill completed');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Fatal error during backfill:', error);
    process.exit(1);
  })
  .finally(async () => {
    await pool.end();
    await redis.quit();
  }); 