require('dotenv').config();
const { Subscriber } = require('zeromq');
const Redis = require('ioredis');
const pino = require('pino');

// Initialize logger
const logger = pino({
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true
    }
  }
});

// Initialize Redis client
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD,
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    return delay;
  }
});

// Initialize ZeroMQ subscriber
const subscriber = new Subscriber();

// Message type handlers
const messageHandlers = {
  TOKEN_STATS: async (data) => {
    const key = `token_stats_${data.address.toLowerCase()}`;
    await redis.set(key, JSON.stringify(data));
    logger.info(`Updated token stats for ${data.address}`);
  },

  SWAPS: async (data) => {
    const key = `swaps_${data.address.toLowerCase()}`;
    // Get existing swaps
    const existingSwaps = await redis.get(key);
    let swaps = existingSwaps ? JSON.parse(existingSwaps) : [];
    
    // Add new swap and maintain order
    swaps.unshift(data.swap);
    
    // Keep only the last 1000 swaps
    if (swaps.length > 1000) {
      swaps = swaps.slice(0, 1000);
    }
    
    await redis.set(key, JSON.stringify(swaps));
    logger.info(`Updated swaps for ${data.address}`);
  },

  HOLDERS: async (data) => {
    const key = `holders_${data.address.toLowerCase()}`;
    await redis.set(key, JSON.stringify(data.holders));
    logger.info(`Updated holders for ${data.address}`);
  },

  OHLCV: async (data) => {
    const key = `ohlcv_${data.address.toLowerCase()}_${data.timeframe}`;
    await redis.set(key, JSON.stringify(data.ohlcv));
    logger.info(`Updated OHLCV for ${data.address} (${data.timeframe})`);
  }
};

async function startProcessor() {
  try {
    // Connect to ZeroMQ publisher
    const zmqEndpoint = process.env.ZMQ_ENDPOINT || 'tcp://localhost:5555';
    await subscriber.connect(zmqEndpoint);
    
    // Subscribe to all message types
    await subscriber.subscribe('TOKEN_STATS');
    await subscriber.subscribe('SWAPS');
    await subscriber.subscribe('HOLDERS');
    await subscriber.subscribe('OHLCV');

    logger.info(`Connected to ZeroMQ publisher at ${zmqEndpoint}`);
    logger.info('Waiting for messages...');

    // Process messages
    for await (const [topic, message] of subscriber) {
      try {
        const messageType = topic.toString();
        const data = JSON.parse(message.toString());

        if (messageHandlers[messageType]) {
          await messageHandlers[messageType](data);
        } else {
          logger.warn(`Unknown message type: ${messageType}`);
        }
      } catch (error) {
        logger.error('Error processing message:', error);
      }
    }
  } catch (error) {
    logger.error('Fatal error:', error);
    process.exit(1);
  }
}

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}, shutting down gracefully...`);

  try {
    await subscriber.close();
    await redis.quit();
    process.exit(0);
  } catch (err) {
    logger.error('Error during shutdown:', err);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.fatal('Uncaught exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.fatal('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the processor
startProcessor(); 