{"name": "liquidlabs-ohlcv-ws-redis", "version": "2.0.0", "description": "Redis-based OHLCV WebSocket API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"@fastify/compress": "^6.2.1", "@fastify/cors": "^8.3.0", "@fastify/helmet": "^11.1.1", "@fastify/swagger": "^8.8.0", "@fastify/swagger-ui": "^1.9.3", "dotenv": "^16.3.1", "fastify": "^4.21.0", "fastify-plugin": "^4.5.0", "fastify-socket.io": "^3.0.0", "ioredis": "^5.3.2", "pino-pretty": "^10.2.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}