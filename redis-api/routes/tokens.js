const fp = require('fastify-plugin');

async function tokenRoutes(fastify, options) {
  // Get token statistics
  fastify.get('/tokens/:address/stats', {
    schema: {
      description: 'Get token statistics',
      tags: ['token'],
      params: {
        type: 'object',
        required: ['address'],
        properties: {
          address: { type: 'string', description: 'Token address' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            address: { type: 'string' },
            name: { type: 'string' },
            symbol: { type: 'string' },
            price: { type: 'number' },
            marketCap: { type: 'number' },
            volume24h: { type: 'number' },
            holders: { type: 'number' },
            totalSupply: { type: 'number' },
            lastUpdated: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { address } = request.params;
    const stats = await fastify.redisHelpers.getTokenStats(address);

    if (!stats) {
      return reply.code(404).send({
        error: 'Token not found',
        statusCode: 404
      });
    }

    return stats;
  });

  // Get token swaps
  fastify.get('/tokens/:address/swaps', {
    schema: {
      description: 'Get token swap history',
      tags: ['token'],
      params: {
        type: 'object',
        required: ['address'],
        properties: {
          address: { type: 'string', description: 'Token address' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', default: 100 },
          offset: { type: 'integer', default: 0 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            swaps: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  txHash: { type: 'string' },
                  timestamp: { type: 'string' },
                  amount: { type: 'number' },
                  price: { type: 'number' },
                  type: { type: 'string' }
                }
              }
            },
            total: { type: 'integer' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { address } = request.params;
    const { limit = 100, offset = 0 } = request.query;
    
    const swaps = await fastify.redisHelpers.getSwaps(address);

    if (!swaps) {
      return reply.code(404).send({
        error: 'Token not found',
        statusCode: 404
      });
    }

    // Paginate the swaps array
    const paginatedSwaps = swaps.slice(offset, offset + limit);

    return {
      swaps: paginatedSwaps,
      total: swaps.length
    };
  });

  // Get token holders
  fastify.get('/tokens/:address/holders', {
    schema: {
      description: 'Get token holder information',
      tags: ['token'],
      params: {
        type: 'object',
        required: ['address'],
        properties: {
          address: { type: 'string', description: 'Token address' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', default: 100 },
          offset: { type: 'integer', default: 0 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            holders: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  address: { type: 'string' },
                  balance: { type: 'number' },
                  percentage: { type: 'number' }
                }
              }
            },
            total: { type: 'integer' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { address } = request.params;
    const { limit = 100, offset = 0 } = request.query;
    
    const holders = await fastify.redisHelpers.getHolders(address);

    if (!holders) {
      return reply.code(404).send({
        error: 'Token not found',
        statusCode: 404
      });
    }

    // Paginate the holders array
    const paginatedHolders = holders.slice(offset, offset + limit);

    return {
      holders: paginatedHolders,
      total: holders.length
    };
  });
}

module.exports = fp(tokenRoutes, {
  name: 'token-routes',
  dependencies: ['redis-plugin']
}); 