const { errorSchema } = require('./common');

const swapSchema = {
  type: 'object',
  properties: {
    token: { 
      type: 'string',
      description: 'Token contract address'
    },
    type: { 
      type: 'string',
      enum: ['purchase', 'sale'],
      description: 'Type of swap (purchase or sale)'
    },
    hype_amount: { 
      type: 'string',
      description: 'Amount of HYPE tokens involved in the swap'
    },
    token_amount: { 
      type: 'string',
      description: 'Amount of tokens involved in the swap'
    },
    price: { 
      type: 'string',
      description: 'Price of the token in HYPE'
    },
    timestamp: { 
      type: 'integer',
      description: 'Unix timestamp of the swap'
    },
    tx_hash: { 
      type: 'string',
      description: 'Transaction hash of the swap'
    },
    token_name: { 
      type: 'string',
      description: 'Name of the token'
    },
    token_symbol: { 
      type: 'string',
      description: 'Symbol of the token'
    },
    metadata: { 
      type: 'object',
      description: 'Token metadata including image URI and other details'
    }
  },
  required: ['token', 'type', 'hype_amount', 'token_amount', 'price', 'timestamp', 'tx_hash']
};

const paginationSchema = {
  type: 'object',
  properties: {
    currentPage: { 
      type: 'integer',
      description: 'Current page number'
    },
    totalPages: { 
      type: 'integer',
      description: 'Total number of pages'
    },
    totalSwaps: { 
      type: 'integer',
      description: 'Total number of swaps'
    },
    limit: { 
      type: 'integer',
      description: 'Number of items per page'
    }
  },
  required: ['currentPage', 'totalPages', 'totalSwaps', 'limit']
};

const walletSwapsResponseSchema = {
  type: 'object',
  properties: {
    pagination: paginationSchema,
    swaps: {
      type: 'array',
      items: swapSchema
    }
  },
  required: ['pagination', 'swaps']
};

const walletAddressParam = {
  type: 'object',
  required: ['walletAddress'],
  properties: {
    walletAddress: {
      type: 'string',
      description: 'Wallet address to get swaps for',
      pattern: '^0x[a-fA-F0-9]{40}$'
    }
  }
};

const walletSwapsQuery = {
  type: 'object',
  properties: {
    token: {
      type: 'string',
      description: 'Optional token address to filter swaps',
      pattern: '^0x[a-fA-F0-9]{40}$'
    },
    page: {
      type: 'integer',
      default: 1,
      minimum: 1,
      description: 'Page number'
    },
    limit: {
      type: 'integer',
      default: 20,
      minimum: 1,
      maximum: 100,
      description: 'Number of items per page'
    },
    sort_order: {
      type: 'string',
      enum: ['asc', 'desc'],
      default: 'desc',
      description: 'Sort order by timestamp'
    }
  }
};

module.exports = {
  swapSchema,
  walletSwapsResponseSchema,
  walletAddressParam,
  walletSwapsQuery
}; 