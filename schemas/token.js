const tokenSchema = {
  type: 'object',
  properties: {
    address: {
      type: 'string',
      pattern: '^0x[a-fA-F0-9]{40}$',
      description: 'Token contract address'
    },
    name: {
      type: 'string',
      description: 'Token name'
    },
    symbol: {
      type: 'string',
      description: 'Token symbol'
    },
    decimals: {
      type: 'integer',
      description: 'Token decimals'
    },
    metadata: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        symbol: { type: 'string' },
        discord: { type: 'string' },
        twitter: { type: 'string' },
        website: { type: 'string' },
        telegram: { type: 'string' },
        image_uri: { type: 'string' },
        description: { type: 'string' },
        creationTimestamp: {
          type: 'object',
          properties: {
            hex: { type: 'string' },
            type: { type: 'string' }
          }
        }
      }
    },
    supplyData: {
      type: 'object',
      properties: {
        tokenReserves: { type: 'string' },
        hypeReserves: { type: 'string' },
        totalSupply: { type: 'string' },
        holders: { type: 'string' }
      }
    },
    creator: {
      type: 'string',
      description: 'Token creator address'
    },
    creationTimestamp: {
      type: 'integer',
      description: 'Token creation timestamp'
    },
    price: {
      type: 'object',
      properties: {
        usd: { type: 'string', description: 'Price in USD' },
        hype: { type: 'string', description: 'Price in HYPE' }
      }
    },
    marketCap: {
      type: 'object',
      properties: {
        usd: { type: 'string', description: 'Market cap in USD' },
        hype: { type: 'string', description: 'Market cap in HYPE' }
      }
    },
    liquidity: {
      type: 'object',
      properties: {
        usd: { type: 'string', description: 'Liquidity in USD' },
        hype: { type: 'string', description: 'Liquidity in HYPE' }
      }
    },
    totalSupply: { type: 'string' },
    holderCount: { type: 'string' },
    timeframes: {
      type: 'object',
      properties: {
        '24h': {
          type: 'object',
          properties: {
            buys: { type: 'integer', description: 'Number of buy trades in last 24h' },
            sells: { type: 'integer', description: 'Number of sell trades in last 24h' },
            volume: { type: 'number', description: '24h volume' },
            priceChange: { type: 'number', description: '24h price change percentage' }
          }
        }
      }
    },
    holders: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
          balance: { type: 'string' },
          percentage: { type: 'string' },
          rank: { type: 'string' }
        }
      }
    },
    top10HoldersPercentage: { type: 'string' },
    isBonded: { type: 'boolean' },
    isFrozen: { type: 'boolean' },
    pair: { type: ['null', 'string'] },
    latestActivityTimestamp: { type: 'integer' },
    financials: {
      type: 'object',
      properties: {
        price: {
          type: 'object',
          properties: {
            usd: { type: 'string' },
            hype: { type: 'string' }
          }
        },
        marketCap: {
          type: 'object',
          properties: {
            usd: { type: 'string' },
            hype: { type: 'string' }
          }
        },
        liquidity: {
          type: 'object',
          properties: {
            usd: { type: 'string' },
            hype: { type: 'string' }
          }
        },
        volume24h: {
          type: 'object',
          properties: {
            usd: { type: 'string' },
            hype: { type: 'string' }
          }
        },
        priceChange24h: { type: 'string' },
        trades24h: {
          type: 'object',
          properties: {
            buys: { type: 'integer' },
            sells: { type: 'integer' },
            total: { type: 'integer' }
          }
        }
      }
    },
    bonding: {
      type: 'object',
      description: 'Token bonding progress information',
      properties: {
        progress: { type: 'string', description: 'Bonding progress percentage (0-100)' },
        hypeProgress: { type: 'string', description: 'HYPE progress percentage (0-100)' },
        tokensSold: { type: 'string', description: 'Number of tokens sold from initial 1B supply' },
        tokensSoldPercentage: { type: 'string', description: 'Percentage of tokens sold' },
        hypeNeededFor100: { type: 'string', description: 'HYPE needed for 100% bonding' },
        currentHypeInPool: { type: 'string', description: 'Current HYPE in pool' },
        bondsAtMcap: { type: 'string', description: 'Market cap at which token will bond (TARGET_MCAP_HYPE_FOR_BONDING * HYPE price)' },
        isBonded: { type: 'boolean', description: 'Whether bonding threshold is reached' },
        constants: {
          type: 'object',
          description: 'Bonding constants used for calculations',
          properties: {
            initialSupply: { type: 'string', description: 'Initial token supply (1B)' },
            targetReserves: { type: 'string', description: 'Target token reserves (1M)' },
            targetBought: { type: 'string', description: 'Target tokens to be bought (990M)' },
            minHypeReserves: { type: 'string', description: 'Minimum HYPE reserves for 0% progress (300)' },
            maxHypeReserves: { type: 'string', description: 'Maximum HYPE reserves for 100% progress (1035.1)' },
            targetMcapHypeForBonding: { type: 'string', description: 'Target market cap in HYPE for bonding (3571)' }
          }
        }
      }
    }
  },
  required: ['address', 'decimals']
};

const tokenListSchema = {
  type: 'array',
  items: tokenSchema
};

const searchTokenSchema = {
  type: 'object',
  properties: {
    address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
    name: { type: 'string' },
    symbol: { type: 'string' },
    decimals: { type: 'integer' },
    image_uri: { type: 'string' }
  },
  required: ['address', 'decimals']
};

module.exports = {
  tokenSchema,
  tokenListSchema,
  searchTokenSchema
}; 