const paginationSchema = {
  type: 'object',
  properties: {
    currentPage: { 
      type: 'integer',
      description: 'Current page number'
    },
    totalPages: { 
      type: 'integer',
      description: 'Total number of pages'
    },
    totalTokens: { 
      type: 'integer',
      description: 'Total number of tokens'
    },
    limit: { 
      type: 'integer',
      description: 'Number of items per page'
    }
  },
  required: ['currentPage', 'totalPages', 'totalTokens', 'limit']
};

const errorSchema = {
  type: 'object',
  properties: {
    error: { 
      type: 'string',
      description: 'Error message'
    }
  },
  required: ['error']
};

const tokenAddressParam = {
  type: 'object',
  required: ['tokenAddress'],
  properties: {
    tokenAddress: {
      type: 'string',
      description: 'Token contract address',
      pattern: '^0x[a-fA-F0-9]{40}$'
    }
  }
};

const paginationQuery = {
  type: 'object',
  properties: {
    page: {
      type: 'integer',
      description: 'Page number for pagination',
      default: 1,
      minimum: 1
    },
    limit: {
      type: 'integer',
      description: 'Number of items per page',
      default: 20,
      minimum: 1,
      maximum: 100
    }
  }
};

const tokenSortQuery = {
  type: 'object',
  properties: {
    sort_by: {
      type: 'string',
      description: 'Field to sort by',
      enum: ['latest_activity', 'mcap', 'swaps_count', 'age', 'volume_24h', 'price_change_24h', 'trades_24h', 'holders_count'],
      default: 'latest_activity'
    },
    sort_order: {
      type: 'string',
      description: 'Sort order',
      enum: ['asc', 'desc'],
      default: 'desc'
    }
  }
};

const tokenFilterQuery = {
  type: 'object',
  properties: {
    min_mcap: {
      type: 'string',
      description: 'Minimum market cap in USD',
      default: '0'
    },
    min_volume_24h: {
      type: 'string',
      description: 'Minimum 24h volume in USD',
      default: '0'
    },
    min_holders: {
      type: 'integer',
      description: 'Minimum number of holders',
      default: 0
    },
    min_swaps: {
      type: 'integer',
      description: 'Minimum number of swaps',
      default: 0
    },
    max_age_days: {
      type: 'integer',
      description: 'Maximum token age in days',
      default: 365
    },
    bonding_status: {
      type: 'string',
      description: 'Filter by bonding status',
      enum: ['all', 'in_progress', 'bonded'],
      default: 'all'
    },
    is_bonded: {
      type: 'boolean',
      description: 'Filter by bonding status (true for bonded tokens, false for unbonded tokens, undefined for all tokens)'
    }
  }
};

const searchQuery = {
  type: 'object',
  required: ['query'],
  properties: {
    query: {
      type: 'string',
      description: 'Search query (token name, symbol, or address)',
      minLength: 1
    },
    limit: {
      type: 'integer',
      description: 'Maximum number of results to return',
      default: 10,
      minimum: 1,
      maximum: 50
    }
  }
};

const timeframeQuery = {
  type: 'object',
  properties: {
    timeframe: {
      type: 'string',
      description: 'Time period for trading statistics',
      enum: ['1h', '6h', '24h', '7d', '30d'],
      default: '24h'
    }
  }
};

module.exports = {
  paginationSchema,
  errorSchema,
  tokenAddressParam,
  paginationQuery,
  tokenSortQuery,
  tokenFilterQuery,
  searchQuery,
  timeframeQuery
}; 