const fastifyPlugin = require('fastify-plugin');

async function websocketPlugin(fastify, options) {
  // User subscriptions map: { socketId: [{ address, interval }, ...] }
  const subscriptions = new Map();
  // Interval timers map: { address+interval: { timer, subscribers: Set(socketId) } }
  const activeIntervals = new Map();

  // Helper function to convert interval string to milliseconds
  function getIntervalMilliseconds(interval) {
    const value = parseInt(interval.slice(0, -1));
    const unit = interval.slice(-1);

    switch (unit) {
      case 's': return value * 1000;
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: return 60 * 1000; // Default to 1 minute
    }
  }

  // Helper function to format a wallet address for display
  function formatWalletAddress(address) {
    // Convert to string and handle null/undefined cases
    if (!address) return '';

    const addressStr = String(address);
    if (addressStr.length < 10) return addressStr;

    return `${addressStr.substring(0, 6)}...${addressStr.substring(addressStr.length - 4)}`;
  }

  // Helper function to validate an Ethereum wallet address
  function isValidEthereumAddress(address) {
    return /^(0x)?[0-9a-fA-F]{40}$/.test(address);
  }

  // Helper function to validate a Bitcoin wallet address
  function isValidBitcoinAddress(address) {
    return /^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/.test(address) ||
      /^bc1[ac-hj-np-z02-9]{39,59}$/.test(address);
  }

  // Helper function to validate wallet address based on chain type
  function validateWalletAddress(address, chain = 'ethereum') {
    if (!address) return false;

    switch (chain.toLowerCase()) {
      case 'ethereum':
      case 'eth':
        return isValidEthereumAddress(address);
      case 'bitcoin':
      case 'btc':
        return isValidBitcoinAddress(address);
      default:
        return address.length >= 30 && address.length <= 64;
    }
  }

  // Method to emit transaction updates
  function emitAddressTransaction(address, txData) {
    if (!address || typeof address !== 'string' || address.trim() === '') {
      fastify.log.warn('Invalid address provided to emitAddressTransaction');
      return;
    }

    const cleanAddress = address.trim();

    if (fastify.io) {
      fastify.io.to(`address-txs:${cleanAddress}`).emit('address_transaction', {
        address: cleanAddress,
        displayAddress: formatWalletAddress(cleanAddress),
        transaction: txData
      });
    } else {
      fastify.log.warn('Socket.IO instance not available for emitAddressTransaction');
    }
  }

  // Decorate Fastify with the method
  fastify.decorate('emitAddressTransaction', emitAddressTransaction);

  // Setup Socket.IO after this plugin is registered
  fastify.after(() => {
    const io = fastify.io;

    if (!io) {
      fastify.log.warn('Socket.IO not available, skipping websocket setup');
      return;
    }

    // Socket.IO connection
    io.on('connection', (socket) => {
      fastify.log.info(`Client connected: ${socket.id}`);

      // Initialize client subscriptions
      subscriptions.set(socket.id, []);

      // Handle subscribe requests
      socket.on('subscribe', async (data) => {
        try {
          const { address, interval = '1m', chain } = data;

          if (!address || typeof address !== 'string' || address.trim() === '') {
            socket.emit('error', { message: 'Valid wallet address is required' });
            return;
          }

          // Trim the address to remove any whitespace
          const cleanAddress = address.trim();

          const userSubs = subscriptions.get(socket.id);
          const subKey = `${cleanAddress}-${interval}`;

          // Check if already subscribed
          if (!userSubs.some(sub => sub.address === cleanAddress && sub.interval === interval)) {
            // Add to user's subscriptions
            userSubs.push({ address: cleanAddress, interval, chain });

            // Check if this subscription already has an active interval
            if (!activeIntervals.has(subKey)) {
              // Create new interval for this subscription
              const intervalTimer = setInterval(async () => {
                try {
                  // Get latest OHLCV data for the wallet
                  const query = `
                    SELECT 
                      time_bucket($1, timestamp) AS time,
                      FIRST(open, timestamp) AS open,
                      MAX(high) AS high,
                      MIN(low) AS low,
                      LAST(close, timestamp) AS close,
                      SUM(volume) AS volume
                    FROM ${process.env.DB_SCHEMA || 'ohlcv_schema'}.price_data
                    WHERE address = $2 AND timestamp > NOW() - INTERVAL '1 day'
                    GROUP BY time
                    ORDER BY time DESC
                    LIMIT 1
                  `;

                  const result = await fastify.pool.query(query, [interval, cleanAddress]);

                  if (result.rows.length > 0) {
                    // Emit to all subscribers of this wallet+interval
                    const subscribers = activeIntervals.get(subKey).subscribers;
                    const update = {
                      type: 'update',
                      address: cleanAddress,
                      displayAddress: formatWalletAddress(cleanAddress),
                      interval,
                      data: result.rows[0]
                    };

                    subscribers.forEach(socketId => {
                      io.to(socketId).emit('ohlcv_update', update);
                    });
                  }
                } catch (error) {
                  fastify.log.error('Error sending OHLCV update for wallet:', error);
                }
              }, getIntervalMilliseconds(interval));

              // Store the interval with an initial subscriber
              activeIntervals.set(subKey, {
                timer: intervalTimer,
                subscribers: new Set([socket.id])
              });
            } else {
              // Add this socket to existing subscribers
              activeIntervals.get(subKey).subscribers.add(socket.id);
            }

            // Send confirmation to client
            socket.emit('subscribed', {
              address: cleanAddress,
              displayAddress: formatWalletAddress(cleanAddress),
              interval,
              chain
            });

            // Send initial data
            try {
              const query = `
                SELECT 
                  time_bucket($1, timestamp) AS time,
                  FIRST(open, timestamp) AS open,
                  MAX(high) AS high,
                  MIN(low) AS low,
                  LAST(close, timestamp) AS close,
                  SUM(volume) AS volume
                FROM ${process.env.DB_SCHEMA || 'ohlcv_schema'}.price_data
                WHERE address = $2 AND timestamp > NOW() - INTERVAL '1 hour'
                GROUP BY time
                ORDER BY time DESC
                LIMIT 1
              `;

              const result = await fastify.pool.query(query, [interval, cleanAddress]);

              if (result.rows.length > 0) {
                socket.emit('ohlcv_update', {
                  type: 'update',
                  address: cleanAddress,
                  displayAddress: formatWalletAddress(cleanAddress),
                  interval,
                  data: result.rows[0]
                });
              } else {
                socket.emit('error', {
                  message: 'No data available for this address',
                  address: cleanAddress
                });
              }
            } catch (error) {
              fastify.log.error('Error sending initial data for wallet:', error);
            }
          }
        } catch (error) {
          fastify.log.error('Error processing wallet subscribe request:', error);
          socket.emit('error', { message: 'Failed to subscribe to wallet' });
        }
      });

      // Handle unsubscribe requests
      socket.on('unsubscribe', (data) => {
        try {
          const { address, interval = '1m' } = data;

          if (!address || typeof address !== 'string' || address.trim() === '') {
            socket.emit('error', { message: 'Valid wallet address is required' });
            return;
          }

          const cleanAddress = address.trim();
          const subKey = `${cleanAddress}-${interval}`;
          const userSubs = subscriptions.get(socket.id);

          if (!userSubs) return;

          // Remove from user's subscriptions
          const subIndex = userSubs.findIndex(sub =>
            sub.address === cleanAddress && sub.interval === interval
          );

          if (subIndex !== -1) {
            userSubs.splice(subIndex, 1);

            // Remove from active intervals
            if (activeIntervals.has(subKey)) {
              const intervalData = activeIntervals.get(subKey);
              intervalData.subscribers.delete(socket.id);

              // If no more subscribers, clear the interval
              if (intervalData.subscribers.size === 0) {
                clearInterval(intervalData.timer);
                activeIntervals.delete(subKey);
              }
            }

            // Send confirmation to client
            socket.emit('unsubscribed', {
              address: cleanAddress,
              displayAddress: formatWalletAddress(cleanAddress),
              interval
            });
          }
        } catch (error) {
          fastify.log.error('Error processing wallet unsubscribe request:', error);
          socket.emit('error', { message: 'Failed to unsubscribe from wallet' });
        }
      });

      // Handle address transaction events
      socket.on('join_address_txs', (address) => {
        if (!address || typeof address !== 'string' || address.trim() === '') {
          socket.emit('error', { message: 'Valid wallet address is required' });
          return;
        }

        const cleanAddress = address.trim();

        // Join a room specific to this wallet's transactions
        socket.join(`address-txs:${cleanAddress}`);
        socket.emit('joined_address_txs', {
          address: cleanAddress,
          displayAddress: formatWalletAddress(cleanAddress)
        });
      });

      socket.on('leave_address_txs', (address) => {
        if (!address || typeof address !== 'string' || address.trim() === '') return;

        const cleanAddress = address.trim();

        socket.leave(`address-txs:${cleanAddress}`);
        socket.emit('left_address_txs', {
          address: cleanAddress,
          displayAddress: formatWalletAddress(cleanAddress)
        });
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        fastify.log.info(`Client disconnected: ${socket.id}`);

        // Clean up all subscriptions for this client
        const userSubs = subscriptions.get(socket.id) || [];

        userSubs.forEach(({ address, interval }) => {
          const subKey = `${address}-${interval}`;

          if (activeIntervals.has(subKey)) {
            const intervalData = activeIntervals.get(subKey);
            intervalData.subscribers.delete(socket.id);

            // If no more subscribers, clear the interval
            if (intervalData.subscribers.size === 0) {
              clearInterval(intervalData.timer);
              activeIntervals.delete(subKey);
            }
          }
        });

        // Remove client from subscriptions map
        subscriptions.delete(socket.id);
      });
    });
  });
}

module.exports = fastifyPlugin(websocketPlugin); 