const fastifyPlugin = require('fastify-plugin');
const { Pool } = require('pg');

async function databasePlugin(fastify, options) {
  // PostgreSQL connection with TimescaleDB
  const pool = new Pool({
    user: process.env.DB_USER || 'ohlcv_user',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'ohlcv_db',
    password: process.env.DB_PASSWORD || 'your_secure_password_here',
    port: process.env.DB_PORT || 5432,
  });

  // Set schema path for the database connection
  pool.on('connect', (client) => {
    client.query(`SET search_path TO ${process.env.DB_SCHEMA || 'ohlcv_schema'}, public`);
  });

  // Test connection
  try {
    await pool.query('SELECT 1');
    fastify.log.info('Database connected successfully');
  } catch (error) {
    fastify.log.error('Database connection failed:', error);
    throw error;
  }

  // Decorate fastify with pool
  fastify.decorate('pool', pool);

  // Close pool on app close
  fastify.addHook('onClose', async (instance, done) => {
    await pool.end();
    done();
  });
}

module.exports = fastifyPlugin(databasePlugin); 