--
-- PostgreSQL database dump
--

-- Dumped from database version 17.5 (Ubuntu 17.5-1.pgdg24.10+1)
-- Dumped by pg_dump version 17.5 (Ubuntu 17.5-1.pgdg24.10+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: timescaledb; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS timescaledb WITH SCHEMA public;


--
-- Name: EXTENSION timescaledb; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION timescaledb IS 'Enables scalable inserts and complex queries for time-series data (Apache 2 Edition)';


--
-- Name: ll_history_schema; Type: SCHEMA; Schema: -; Owner: liquidlaunch_history
--

CREATE SCHEMA ll_history_schema;


ALTER SCHEMA ll_history_schema OWNER TO liquidlaunch_history;

--
-- Name: update_token_holder_count(); Type: FUNCTION; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE FUNCTION ll_history_schema.update_token_holder_count() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
        BEGIN
          UPDATE realtime_token_data
          SET holder_count = (
            SELECT COUNT(DISTINCT holder_address)
            FROM token_holders
            WHERE token_address = NEW.token_address
            AND balance > 0
          )
          WHERE token_address = NEW.token_address;
          RETURN NEW;
        END;
        $$;


ALTER FUNCTION ll_history_schema.update_token_holder_count() OWNER TO liquidlaunch_history;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: price_data; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.price_data (
    "timestamp" timestamp with time zone NOT NULL,
    address text NOT NULL,
    open double precision NOT NULL,
    high double precision NOT NULL,
    low double precision NOT NULL,
    close double precision NOT NULL,
    volume double precision NOT NULL
);


ALTER TABLE ll_history_schema.price_data OWNER TO liquidlaunch_history;

--
-- Name: _hyper_1_1_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE TABLE _timescaledb_internal._hyper_1_1_chunk (
    CONSTRAINT constraint_1 CHECK ((("timestamp" >= '2025-05-08 00:00:00+00'::timestamp with time zone) AND ("timestamp" < '2025-05-15 00:00:00+00'::timestamp with time zone)))
)
INHERITS (ll_history_schema.price_data);


ALTER TABLE _timescaledb_internal._hyper_1_1_chunk OWNER TO liquidlaunch_history;

--
-- Name: _hyper_1_2_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE TABLE _timescaledb_internal._hyper_1_2_chunk (
    CONSTRAINT constraint_2 CHECK ((("timestamp" >= '2025-05-15 00:00:00+00'::timestamp with time zone) AND ("timestamp" < '2025-05-22 00:00:00+00'::timestamp with time zone)))
)
INHERITS (ll_history_schema.price_data);


ALTER TABLE _timescaledb_internal._hyper_1_2_chunk OWNER TO liquidlaunch_history;

--
-- Name: _hyper_1_3_chunk; Type: TABLE; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE TABLE _timescaledb_internal._hyper_1_3_chunk (
    CONSTRAINT constraint_3 CHECK ((("timestamp" >= '2025-05-22 00:00:00+00'::timestamp with time zone) AND ("timestamp" < '2025-05-29 00:00:00+00'::timestamp with time zone)))
)
INHERITS (ll_history_schema.price_data);


ALTER TABLE _timescaledb_internal._hyper_1_3_chunk OWNER TO liquidlaunch_history;

--
-- Name: indexer_status; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.indexer_status (
    key character varying(255) NOT NULL,
    value_numeric bigint,
    value_text text,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE ll_history_schema.indexer_status OWNER TO liquidlaunch_history;

--
-- Name: liquidity_burned_events; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.liquidity_burned_events (
    id bigint NOT NULL,
    token character(42) NOT NULL,
    amount numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
)
PARTITION BY RANGE ("timestamp");


ALTER TABLE ll_history_schema.liquidity_burned_events OWNER TO liquidlaunch_history;

--
-- Name: liquidity_burned_events_id_seq; Type: SEQUENCE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE SEQUENCE ll_history_schema.liquidity_burned_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE ll_history_schema.liquidity_burned_events_id_seq OWNER TO liquidlaunch_history;

--
-- Name: liquidity_burned_events_id_seq; Type: SEQUENCE OWNED BY; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER SEQUENCE ll_history_schema.liquidity_burned_events_id_seq OWNED BY ll_history_schema.liquidity_burned_events.id;


--
-- Name: liquidity_burned_events_y2024; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.liquidity_burned_events_y2024 (
    id bigint DEFAULT nextval('ll_history_schema.liquidity_burned_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    amount numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.liquidity_burned_events_y2024 OWNER TO liquidlaunch_history;

--
-- Name: liquidity_burned_events_y2025; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.liquidity_burned_events_y2025 (
    id bigint DEFAULT nextval('ll_history_schema.liquidity_burned_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    amount numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.liquidity_burned_events_y2025 OWNER TO liquidlaunch_history;

--
-- Name: liquidity_burned_events_y2026; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.liquidity_burned_events_y2026 (
    id bigint DEFAULT nextval('ll_history_schema.liquidity_burned_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    amount numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.liquidity_burned_events_y2026 OWNER TO liquidlaunch_history;

--
-- Name: pair_created_events; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.pair_created_events (
    id bigint NOT NULL,
    token character(42) NOT NULL,
    pair character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
)
PARTITION BY RANGE ("timestamp");


ALTER TABLE ll_history_schema.pair_created_events OWNER TO liquidlaunch_history;

--
-- Name: pair_created_events_id_seq; Type: SEQUENCE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE SEQUENCE ll_history_schema.pair_created_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE ll_history_schema.pair_created_events_id_seq OWNER TO liquidlaunch_history;

--
-- Name: pair_created_events_id_seq; Type: SEQUENCE OWNED BY; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER SEQUENCE ll_history_schema.pair_created_events_id_seq OWNED BY ll_history_schema.pair_created_events.id;


--
-- Name: pair_created_events_y2024; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.pair_created_events_y2024 (
    id bigint DEFAULT nextval('ll_history_schema.pair_created_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    pair character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.pair_created_events_y2024 OWNER TO liquidlaunch_history;

--
-- Name: pair_created_events_y2025; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.pair_created_events_y2025 (
    id bigint DEFAULT nextval('ll_history_schema.pair_created_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    pair character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.pair_created_events_y2025 OWNER TO liquidlaunch_history;

--
-- Name: pair_created_events_y2026; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.pair_created_events_y2026 (
    id bigint DEFAULT nextval('ll_history_schema.pair_created_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    pair character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.pair_created_events_y2026 OWNER TO liquidlaunch_history;

--
-- Name: realtime_token_data; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.realtime_token_data (
    token_address character(42) NOT NULL,
    name character varying(100) NOT NULL,
    symbol character varying(255) NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    market_cap_hype numeric(36,18),
    price_hype numeric(36,18),
    holder_count integer DEFAULT 0,
    total_supply numeric(36,18),
    hype_reserves numeric(36,18),
    token_reserves numeric(36,18),
    last_updated timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE ll_history_schema.realtime_token_data OWNER TO liquidlaunch_history;

--
-- Name: swaps; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.swaps (
    id bigint NOT NULL,
    token character(42) NOT NULL,
    type text,
    trader character(42) NOT NULL,
    hype_amount numeric(36,18) NOT NULL,
    token_amount numeric(36,18) NOT NULL,
    price numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    hype_reserves numeric(36,18) NOT NULL,
    token_reserves numeric(36,18) NOT NULL,
    total_supply numeric(36,18),
    token_name character varying(100) NOT NULL,
    token_symbol character varying(255) NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL,
    CONSTRAINT swaps_type_check CHECK ((type = ANY (ARRAY['purchase'::text, 'sale'::text])))
)
PARTITION BY RANGE ("timestamp");


ALTER TABLE ll_history_schema.swaps OWNER TO liquidlaunch_history;

--
-- Name: swaps_id_seq; Type: SEQUENCE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE SEQUENCE ll_history_schema.swaps_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE ll_history_schema.swaps_id_seq OWNER TO liquidlaunch_history;

--
-- Name: swaps_id_seq; Type: SEQUENCE OWNED BY; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER SEQUENCE ll_history_schema.swaps_id_seq OWNED BY ll_history_schema.swaps.id;


--
-- Name: swaps_y2024; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.swaps_y2024 (
    id bigint DEFAULT nextval('ll_history_schema.swaps_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    type text,
    trader character(42) NOT NULL,
    hype_amount numeric(36,18) NOT NULL,
    token_amount numeric(36,18) NOT NULL,
    price numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    hype_reserves numeric(36,18) NOT NULL,
    token_reserves numeric(36,18) NOT NULL,
    total_supply numeric(36,18),
    token_name character varying(100) NOT NULL,
    token_symbol character varying(255) NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL,
    CONSTRAINT swaps_type_check CHECK ((type = ANY (ARRAY['purchase'::text, 'sale'::text])))
);


ALTER TABLE ll_history_schema.swaps_y2024 OWNER TO liquidlaunch_history;

--
-- Name: swaps_y2025; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.swaps_y2025 (
    id bigint DEFAULT nextval('ll_history_schema.swaps_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    type text,
    trader character(42) NOT NULL,
    hype_amount numeric(36,18) NOT NULL,
    token_amount numeric(36,18) NOT NULL,
    price numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    hype_reserves numeric(36,18) NOT NULL,
    token_reserves numeric(36,18) NOT NULL,
    total_supply numeric(36,18),
    token_name character varying(100) NOT NULL,
    token_symbol character varying(255) NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL,
    CONSTRAINT swaps_type_check CHECK ((type = ANY (ARRAY['purchase'::text, 'sale'::text])))
);


ALTER TABLE ll_history_schema.swaps_y2025 OWNER TO liquidlaunch_history;

--
-- Name: swaps_y2026; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.swaps_y2026 (
    id bigint DEFAULT nextval('ll_history_schema.swaps_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    type text,
    trader character(42) NOT NULL,
    hype_amount numeric(36,18) NOT NULL,
    token_amount numeric(36,18) NOT NULL,
    price numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    hype_reserves numeric(36,18) NOT NULL,
    token_reserves numeric(36,18) NOT NULL,
    total_supply numeric(36,18),
    token_name character varying(100) NOT NULL,
    token_symbol character varying(255) NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL,
    CONSTRAINT swaps_type_check CHECK ((type = ANY (ARRAY['purchase'::text, 'sale'::text])))
);


ALTER TABLE ll_history_schema.swaps_y2026 OWNER TO liquidlaunch_history;

--
-- Name: token_24h_volume; Type: MATERIALIZED VIEW; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE MATERIALIZED VIEW ll_history_schema.token_24h_volume AS
 SELECT token,
    sum(
        CASE
            WHEN (type = 'purchase'::text) THEN (hype_amount)::numeric
            ELSE (0)::numeric
        END) AS buy_volume,
    sum(
        CASE
            WHEN (type = 'sale'::text) THEN (hype_amount)::numeric
            ELSE (0)::numeric
        END) AS sell_volume,
    count(*) AS total_swaps
   FROM ll_history_schema.swaps
  WHERE (("timestamp")::numeric >= EXTRACT(epoch FROM (now() - '24:00:00'::interval)))
  GROUP BY token
  WITH NO DATA;


ALTER MATERIALIZED VIEW ll_history_schema.token_24h_volume OWNER TO liquidlaunch_history;

--
-- Name: token_bonded_events; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_bonded_events (
    id bigint NOT NULL,
    token character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
)
PARTITION BY RANGE ("timestamp");


ALTER TABLE ll_history_schema.token_bonded_events OWNER TO liquidlaunch_history;

--
-- Name: token_bonded_events_id_seq; Type: SEQUENCE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE SEQUENCE ll_history_schema.token_bonded_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE ll_history_schema.token_bonded_events_id_seq OWNER TO liquidlaunch_history;

--
-- Name: token_bonded_events_id_seq; Type: SEQUENCE OWNED BY; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER SEQUENCE ll_history_schema.token_bonded_events_id_seq OWNED BY ll_history_schema.token_bonded_events.id;


--
-- Name: token_bonded_events_y2024; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_bonded_events_y2024 (
    id bigint DEFAULT nextval('ll_history_schema.token_bonded_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_bonded_events_y2024 OWNER TO liquidlaunch_history;

--
-- Name: token_bonded_events_y2025; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_bonded_events_y2025 (
    id bigint DEFAULT nextval('ll_history_schema.token_bonded_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_bonded_events_y2025 OWNER TO liquidlaunch_history;

--
-- Name: token_bonded_events_y2026; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_bonded_events_y2026 (
    id bigint DEFAULT nextval('ll_history_schema.token_bonded_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_bonded_events_y2026 OWNER TO liquidlaunch_history;

--
-- Name: token_burned_events; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_burned_events (
    id bigint NOT NULL,
    token character(42) NOT NULL,
    amount numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
)
PARTITION BY RANGE ("timestamp");


ALTER TABLE ll_history_schema.token_burned_events OWNER TO liquidlaunch_history;

--
-- Name: token_burned_events_id_seq; Type: SEQUENCE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE SEQUENCE ll_history_schema.token_burned_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE ll_history_schema.token_burned_events_id_seq OWNER TO liquidlaunch_history;

--
-- Name: token_burned_events_id_seq; Type: SEQUENCE OWNED BY; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER SEQUENCE ll_history_schema.token_burned_events_id_seq OWNED BY ll_history_schema.token_burned_events.id;


--
-- Name: token_burned_events_y2024; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_burned_events_y2024 (
    id bigint DEFAULT nextval('ll_history_schema.token_burned_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    amount numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_burned_events_y2024 OWNER TO liquidlaunch_history;

--
-- Name: token_burned_events_y2025; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_burned_events_y2025 (
    id bigint DEFAULT nextval('ll_history_schema.token_burned_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    amount numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_burned_events_y2025 OWNER TO liquidlaunch_history;

--
-- Name: token_burned_events_y2026; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_burned_events_y2026 (
    id bigint DEFAULT nextval('ll_history_schema.token_burned_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    amount numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_burned_events_y2026 OWNER TO liquidlaunch_history;

--
-- Name: token_frozen_events; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_frozen_events (
    id bigint NOT NULL,
    token character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
)
PARTITION BY RANGE ("timestamp");


ALTER TABLE ll_history_schema.token_frozen_events OWNER TO liquidlaunch_history;

--
-- Name: token_frozen_events_id_seq; Type: SEQUENCE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE SEQUENCE ll_history_schema.token_frozen_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE ll_history_schema.token_frozen_events_id_seq OWNER TO liquidlaunch_history;

--
-- Name: token_frozen_events_id_seq; Type: SEQUENCE OWNED BY; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER SEQUENCE ll_history_schema.token_frozen_events_id_seq OWNED BY ll_history_schema.token_frozen_events.id;


--
-- Name: token_frozen_events_y2024; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_frozen_events_y2024 (
    id bigint DEFAULT nextval('ll_history_schema.token_frozen_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_frozen_events_y2024 OWNER TO liquidlaunch_history;

--
-- Name: token_frozen_events_y2025; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_frozen_events_y2025 (
    id bigint DEFAULT nextval('ll_history_schema.token_frozen_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_frozen_events_y2025 OWNER TO liquidlaunch_history;

--
-- Name: token_frozen_events_y2026; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_frozen_events_y2026 (
    id bigint DEFAULT nextval('ll_history_schema.token_frozen_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.token_frozen_events_y2026 OWNER TO liquidlaunch_history;

--
-- Name: token_holders; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_holders (
    token_address character(42) NOT NULL,
    holder_address character(42) NOT NULL,
    balance numeric(36,18) NOT NULL,
    last_updated timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE ll_history_schema.token_holders OWNER TO liquidlaunch_history;

--
-- Name: token_metadata_history; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.token_metadata_history (
    id bigint NOT NULL,
    token character(42) NOT NULL,
    name character varying(100) NOT NULL,
    symbol character varying(255) NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    updated_at integer NOT NULL,
    tx_hash character(66) NOT NULL
);


ALTER TABLE ll_history_schema.token_metadata_history OWNER TO liquidlaunch_history;

--
-- Name: token_metadata_history_id_seq; Type: SEQUENCE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE SEQUENCE ll_history_schema.token_metadata_history_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE ll_history_schema.token_metadata_history_id_seq OWNER TO liquidlaunch_history;

--
-- Name: token_metadata_history_id_seq; Type: SEQUENCE OWNED BY; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER SEQUENCE ll_history_schema.token_metadata_history_id_seq OWNED BY ll_history_schema.token_metadata_history.id;


--
-- Name: tokens; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.tokens (
    address character(42) NOT NULL,
    name character varying(100) NOT NULL,
    symbol character varying(255) NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    creator character(42) NOT NULL,
    creation_timestamp integer NOT NULL,
    decimals integer NOT NULL
);


ALTER TABLE ll_history_schema.tokens OWNER TO liquidlaunch_history;

--
-- Name: top_10_pnl; Type: MATERIALIZED VIEW; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE MATERIALIZED VIEW ll_history_schema.top_10_pnl AS
 WITH trader_actions AS (
         SELECT swaps.trader,
            sum(
                CASE
                    WHEN (swaps.type = 'purchase'::text) THEN (swaps.hype_amount)::numeric
                    ELSE (0)::numeric
                END) AS total_spent,
            sum(
                CASE
                    WHEN (swaps.type = 'sale'::text) THEN (swaps.hype_amount)::numeric
                    ELSE (0)::numeric
                END) AS total_received,
            count(DISTINCT swaps.token) AS unique_tokens_traded,
            count(*) AS total_trades,
            max(swaps."timestamp") AS last_trade_timestamp
           FROM ll_history_schema.swaps
          GROUP BY swaps.trader
        ), trader_pnl AS (
         SELECT trader_actions.trader,
            (trader_actions.total_received - trader_actions.total_spent) AS pnl,
            trader_actions.total_spent,
            trader_actions.total_received,
            trader_actions.unique_tokens_traded,
            trader_actions.total_trades,
            trader_actions.last_trade_timestamp,
                CASE
                    WHEN (trader_actions.total_spent > (0)::numeric) THEN (((trader_actions.total_received - trader_actions.total_spent) / trader_actions.total_spent) * (100)::numeric)
                    ELSE (0)::numeric
                END AS roi_percentage
           FROM trader_actions
          WHERE (trader_actions.total_trades >= 3)
        )
 SELECT trader,
    pnl,
    total_spent,
    total_received,
    unique_tokens_traded,
    total_trades,
    to_timestamp((last_trade_timestamp)::double precision) AS last_trade_time,
    roi_percentage
   FROM trader_pnl
  WHERE (pnl <> (0)::numeric)
  ORDER BY pnl DESC
 LIMIT 10
  WITH NO DATA;


ALTER MATERIALIZED VIEW ll_history_schema.top_10_pnl OWNER TO liquidlaunch_history;

--
-- Name: transfer_events; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.transfer_events (
    id bigint NOT NULL,
    token character(42) NOT NULL,
    from_address character(42) NOT NULL,
    to_address character(42) NOT NULL,
    value numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
)
PARTITION BY RANGE ("timestamp");


ALTER TABLE ll_history_schema.transfer_events OWNER TO liquidlaunch_history;

--
-- Name: transfer_events_id_seq; Type: SEQUENCE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE SEQUENCE ll_history_schema.transfer_events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE ll_history_schema.transfer_events_id_seq OWNER TO liquidlaunch_history;

--
-- Name: transfer_events_id_seq; Type: SEQUENCE OWNED BY; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER SEQUENCE ll_history_schema.transfer_events_id_seq OWNED BY ll_history_schema.transfer_events.id;


--
-- Name: transfer_events_y2024; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.transfer_events_y2024 (
    id bigint DEFAULT nextval('ll_history_schema.transfer_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    from_address character(42) NOT NULL,
    to_address character(42) NOT NULL,
    value numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.transfer_events_y2024 OWNER TO liquidlaunch_history;

--
-- Name: transfer_events_y2025; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.transfer_events_y2025 (
    id bigint DEFAULT nextval('ll_history_schema.transfer_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    from_address character(42) NOT NULL,
    to_address character(42) NOT NULL,
    value numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.transfer_events_y2025 OWNER TO liquidlaunch_history;

--
-- Name: transfer_events_y2026; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.transfer_events_y2026 (
    id bigint DEFAULT nextval('ll_history_schema.transfer_events_id_seq'::regclass) NOT NULL,
    token character(42) NOT NULL,
    from_address character(42) NOT NULL,
    to_address character(42) NOT NULL,
    value numeric(36,18) NOT NULL,
    "timestamp" integer NOT NULL,
    tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL
);


ALTER TABLE ll_history_schema.transfer_events_y2026 OWNER TO liquidlaunch_history;

--
-- Name: wallet_first_interaction; Type: TABLE; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TABLE ll_history_schema.wallet_first_interaction (
    wallet_address character(42) NOT NULL,
    first_interaction_time integer NOT NULL,
    first_tx_hash character(66) NOT NULL,
    block_number numeric(36,18) NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE ll_history_schema.wallet_first_interaction OWNER TO liquidlaunch_history;

--
-- Name: liquidity_burned_events_y2024; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.liquidity_burned_events ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2024 FOR VALUES FROM (1704067200) TO (1735689600);


--
-- Name: liquidity_burned_events_y2025; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.liquidity_burned_events ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2025 FOR VALUES FROM (1735689600) TO (1767225600);


--
-- Name: liquidity_burned_events_y2026; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.liquidity_burned_events ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2026 FOR VALUES FROM (1767225600) TO (1798761600);


--
-- Name: pair_created_events_y2024; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.pair_created_events ATTACH PARTITION ll_history_schema.pair_created_events_y2024 FOR VALUES FROM (1704067200) TO (1735689600);


--
-- Name: pair_created_events_y2025; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.pair_created_events ATTACH PARTITION ll_history_schema.pair_created_events_y2025 FOR VALUES FROM (1735689600) TO (1767225600);


--
-- Name: pair_created_events_y2026; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.pair_created_events ATTACH PARTITION ll_history_schema.pair_created_events_y2026 FOR VALUES FROM (1767225600) TO (1798761600);


--
-- Name: swaps_y2024; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.swaps ATTACH PARTITION ll_history_schema.swaps_y2024 FOR VALUES FROM (1704067200) TO (1735689600);


--
-- Name: swaps_y2025; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.swaps ATTACH PARTITION ll_history_schema.swaps_y2025 FOR VALUES FROM (1735689600) TO (1767225600);


--
-- Name: swaps_y2026; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.swaps ATTACH PARTITION ll_history_schema.swaps_y2026 FOR VALUES FROM (1767225600) TO (1798761600);


--
-- Name: token_bonded_events_y2024; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_bonded_events ATTACH PARTITION ll_history_schema.token_bonded_events_y2024 FOR VALUES FROM (1704067200) TO (1735689600);


--
-- Name: token_bonded_events_y2025; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_bonded_events ATTACH PARTITION ll_history_schema.token_bonded_events_y2025 FOR VALUES FROM (1735689600) TO (1767225600);


--
-- Name: token_bonded_events_y2026; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_bonded_events ATTACH PARTITION ll_history_schema.token_bonded_events_y2026 FOR VALUES FROM (1767225600) TO (1798761600);


--
-- Name: token_burned_events_y2024; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_burned_events ATTACH PARTITION ll_history_schema.token_burned_events_y2024 FOR VALUES FROM (1704067200) TO (1735689600);


--
-- Name: token_burned_events_y2025; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_burned_events ATTACH PARTITION ll_history_schema.token_burned_events_y2025 FOR VALUES FROM (1735689600) TO (1767225600);


--
-- Name: token_burned_events_y2026; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_burned_events ATTACH PARTITION ll_history_schema.token_burned_events_y2026 FOR VALUES FROM (1767225600) TO (1798761600);


--
-- Name: token_frozen_events_y2024; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_frozen_events ATTACH PARTITION ll_history_schema.token_frozen_events_y2024 FOR VALUES FROM (1704067200) TO (1735689600);


--
-- Name: token_frozen_events_y2025; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_frozen_events ATTACH PARTITION ll_history_schema.token_frozen_events_y2025 FOR VALUES FROM (1735689600) TO (1767225600);


--
-- Name: token_frozen_events_y2026; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_frozen_events ATTACH PARTITION ll_history_schema.token_frozen_events_y2026 FOR VALUES FROM (1767225600) TO (1798761600);


--
-- Name: transfer_events_y2024; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.transfer_events ATTACH PARTITION ll_history_schema.transfer_events_y2024 FOR VALUES FROM (1704067200) TO (1735689600);


--
-- Name: transfer_events_y2025; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.transfer_events ATTACH PARTITION ll_history_schema.transfer_events_y2025 FOR VALUES FROM (1735689600) TO (1767225600);


--
-- Name: transfer_events_y2026; Type: TABLE ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.transfer_events ATTACH PARTITION ll_history_schema.transfer_events_y2026 FOR VALUES FROM (1767225600) TO (1798761600);


--
-- Name: liquidity_burned_events id; Type: DEFAULT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.liquidity_burned_events ALTER COLUMN id SET DEFAULT nextval('ll_history_schema.liquidity_burned_events_id_seq'::regclass);


--
-- Name: pair_created_events id; Type: DEFAULT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.pair_created_events ALTER COLUMN id SET DEFAULT nextval('ll_history_schema.pair_created_events_id_seq'::regclass);


--
-- Name: swaps id; Type: DEFAULT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.swaps ALTER COLUMN id SET DEFAULT nextval('ll_history_schema.swaps_id_seq'::regclass);


--
-- Name: token_bonded_events id; Type: DEFAULT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_bonded_events ALTER COLUMN id SET DEFAULT nextval('ll_history_schema.token_bonded_events_id_seq'::regclass);


--
-- Name: token_burned_events id; Type: DEFAULT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_burned_events ALTER COLUMN id SET DEFAULT nextval('ll_history_schema.token_burned_events_id_seq'::regclass);


--
-- Name: token_frozen_events id; Type: DEFAULT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_frozen_events ALTER COLUMN id SET DEFAULT nextval('ll_history_schema.token_frozen_events_id_seq'::regclass);


--
-- Name: token_metadata_history id; Type: DEFAULT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_metadata_history ALTER COLUMN id SET DEFAULT nextval('ll_history_schema.token_metadata_history_id_seq'::regclass);


--
-- Name: transfer_events id; Type: DEFAULT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.transfer_events ALTER COLUMN id SET DEFAULT nextval('ll_history_schema.transfer_events_id_seq'::regclass);


--
-- Name: indexer_status indexer_status_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.indexer_status
    ADD CONSTRAINT indexer_status_pkey PRIMARY KEY (key);


--
-- Name: liquidity_burned_events liquidity_burned_events_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.liquidity_burned_events
    ADD CONSTRAINT liquidity_burned_events_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: liquidity_burned_events_y2024 liquidity_burned_events_y2024_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.liquidity_burned_events_y2024
    ADD CONSTRAINT liquidity_burned_events_y2024_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: liquidity_burned_events_y2025 liquidity_burned_events_y2025_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.liquidity_burned_events_y2025
    ADD CONSTRAINT liquidity_burned_events_y2025_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: liquidity_burned_events_y2026 liquidity_burned_events_y2026_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.liquidity_burned_events_y2026
    ADD CONSTRAINT liquidity_burned_events_y2026_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: pair_created_events pair_created_events_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.pair_created_events
    ADD CONSTRAINT pair_created_events_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: pair_created_events_y2024 pair_created_events_y2024_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.pair_created_events_y2024
    ADD CONSTRAINT pair_created_events_y2024_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: pair_created_events_y2025 pair_created_events_y2025_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.pair_created_events_y2025
    ADD CONSTRAINT pair_created_events_y2025_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: pair_created_events_y2026 pair_created_events_y2026_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.pair_created_events_y2026
    ADD CONSTRAINT pair_created_events_y2026_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: realtime_token_data realtime_token_data_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.realtime_token_data
    ADD CONSTRAINT realtime_token_data_pkey PRIMARY KEY (token_address);


--
-- Name: swaps swaps_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.swaps
    ADD CONSTRAINT swaps_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: swaps_y2024 swaps_y2024_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.swaps_y2024
    ADD CONSTRAINT swaps_y2024_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: swaps_y2025 swaps_y2025_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.swaps_y2025
    ADD CONSTRAINT swaps_y2025_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: swaps_y2026 swaps_y2026_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.swaps_y2026
    ADD CONSTRAINT swaps_y2026_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_bonded_events token_bonded_events_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_bonded_events
    ADD CONSTRAINT token_bonded_events_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_bonded_events_y2024 token_bonded_events_y2024_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_bonded_events_y2024
    ADD CONSTRAINT token_bonded_events_y2024_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_bonded_events_y2025 token_bonded_events_y2025_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_bonded_events_y2025
    ADD CONSTRAINT token_bonded_events_y2025_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_bonded_events_y2026 token_bonded_events_y2026_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_bonded_events_y2026
    ADD CONSTRAINT token_bonded_events_y2026_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_burned_events token_burned_events_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_burned_events
    ADD CONSTRAINT token_burned_events_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_burned_events_y2024 token_burned_events_y2024_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_burned_events_y2024
    ADD CONSTRAINT token_burned_events_y2024_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_burned_events_y2025 token_burned_events_y2025_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_burned_events_y2025
    ADD CONSTRAINT token_burned_events_y2025_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_burned_events_y2026 token_burned_events_y2026_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_burned_events_y2026
    ADD CONSTRAINT token_burned_events_y2026_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_frozen_events token_frozen_events_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_frozen_events
    ADD CONSTRAINT token_frozen_events_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_frozen_events_y2024 token_frozen_events_y2024_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_frozen_events_y2024
    ADD CONSTRAINT token_frozen_events_y2024_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_frozen_events_y2025 token_frozen_events_y2025_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_frozen_events_y2025
    ADD CONSTRAINT token_frozen_events_y2025_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_frozen_events_y2026 token_frozen_events_y2026_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_frozen_events_y2026
    ADD CONSTRAINT token_frozen_events_y2026_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: token_holders token_holders_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_holders
    ADD CONSTRAINT token_holders_pkey PRIMARY KEY (token_address, holder_address);


--
-- Name: token_metadata_history token_metadata_history_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_metadata_history
    ADD CONSTRAINT token_metadata_history_pkey PRIMARY KEY (id);


--
-- Name: tokens tokens_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.tokens
    ADD CONSTRAINT tokens_pkey PRIMARY KEY (address);


--
-- Name: transfer_events transfer_events_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.transfer_events
    ADD CONSTRAINT transfer_events_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: transfer_events_y2024 transfer_events_y2024_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.transfer_events_y2024
    ADD CONSTRAINT transfer_events_y2024_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: transfer_events_y2025 transfer_events_y2025_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.transfer_events_y2025
    ADD CONSTRAINT transfer_events_y2025_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: transfer_events_y2026 transfer_events_y2026_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.transfer_events_y2026
    ADD CONSTRAINT transfer_events_y2026_pkey PRIMARY KEY ("timestamp", id);


--
-- Name: wallet_first_interaction wallet_first_interaction_pkey; Type: CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.wallet_first_interaction
    ADD CONSTRAINT wallet_first_interaction_pkey PRIMARY KEY (wallet_address);


--
-- Name: _hyper_1_1_chunk_idx_price_data_address_timestamp; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE INDEX _hyper_1_1_chunk_idx_price_data_address_timestamp ON _timescaledb_internal._hyper_1_1_chunk USING btree (address, "timestamp" DESC);


--
-- Name: _hyper_1_1_chunk_idx_price_data_timestamp_address_unique; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX _hyper_1_1_chunk_idx_price_data_timestamp_address_unique ON _timescaledb_internal._hyper_1_1_chunk USING btree ("timestamp", address);


--
-- Name: _hyper_1_1_chunk_price_data_timestamp_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE INDEX _hyper_1_1_chunk_price_data_timestamp_idx ON _timescaledb_internal._hyper_1_1_chunk USING btree ("timestamp" DESC);


--
-- Name: _hyper_1_2_chunk_idx_price_data_address_timestamp; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE INDEX _hyper_1_2_chunk_idx_price_data_address_timestamp ON _timescaledb_internal._hyper_1_2_chunk USING btree (address, "timestamp" DESC);


--
-- Name: _hyper_1_2_chunk_idx_price_data_timestamp_address_unique; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX _hyper_1_2_chunk_idx_price_data_timestamp_address_unique ON _timescaledb_internal._hyper_1_2_chunk USING btree ("timestamp", address);


--
-- Name: _hyper_1_2_chunk_price_data_timestamp_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE INDEX _hyper_1_2_chunk_price_data_timestamp_idx ON _timescaledb_internal._hyper_1_2_chunk USING btree ("timestamp" DESC);


--
-- Name: _hyper_1_3_chunk_idx_price_data_address_timestamp; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE INDEX _hyper_1_3_chunk_idx_price_data_address_timestamp ON _timescaledb_internal._hyper_1_3_chunk USING btree (address, "timestamp" DESC);


--
-- Name: _hyper_1_3_chunk_idx_price_data_timestamp_address_unique; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX _hyper_1_3_chunk_idx_price_data_timestamp_address_unique ON _timescaledb_internal._hyper_1_3_chunk USING btree ("timestamp", address);


--
-- Name: _hyper_1_3_chunk_price_data_timestamp_idx; Type: INDEX; Schema: _timescaledb_internal; Owner: liquidlaunch_history
--

CREATE INDEX _hyper_1_3_chunk_price_data_timestamp_idx ON _timescaledb_internal._hyper_1_3_chunk USING btree ("timestamp" DESC);


--
-- Name: idx_liquidity_burned_conflict; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_liquidity_burned_conflict ON ONLY ll_history_schema.liquidity_burned_events USING btree (tx_hash, token, "timestamp");


--
-- Name: idx_liquidity_burned_token; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_liquidity_burned_token ON ONLY ll_history_schema.liquidity_burned_events USING btree (token);


--
-- Name: idx_metadata_history_token; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_metadata_history_token ON ll_history_schema.token_metadata_history USING btree (token);


--
-- Name: idx_metadata_history_tx_hash_token; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_metadata_history_tx_hash_token ON ll_history_schema.token_metadata_history USING btree (tx_hash, token);


--
-- Name: idx_metadata_history_updated_at; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_metadata_history_updated_at ON ll_history_schema.token_metadata_history USING btree (updated_at);


--
-- Name: idx_pair_created_conflict; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_pair_created_conflict ON ONLY ll_history_schema.pair_created_events USING btree (tx_hash, token, pair, "timestamp");


--
-- Name: idx_pair_created_token; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_pair_created_token ON ONLY ll_history_schema.pair_created_events USING btree (token);


--
-- Name: idx_price_data_address_timestamp; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_price_data_address_timestamp ON ll_history_schema.price_data USING btree (address, "timestamp" DESC);


--
-- Name: idx_price_data_timestamp_address_unique; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_price_data_timestamp_address_unique ON ll_history_schema.price_data USING btree ("timestamp", address);


--
-- Name: idx_realtime_token_data_holder_count; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_realtime_token_data_holder_count ON ll_history_schema.realtime_token_data USING btree (holder_count DESC);


--
-- Name: idx_realtime_token_data_market_cap; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_realtime_token_data_market_cap ON ll_history_schema.realtime_token_data USING btree (market_cap_hype DESC);


--
-- Name: idx_realtime_token_data_price; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_realtime_token_data_price ON ll_history_schema.realtime_token_data USING btree (price_hype DESC);


--
-- Name: idx_swaps_conflict_target; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_swaps_conflict_target ON ONLY ll_history_schema.swaps USING btree (tx_hash, type, trader, token, "timestamp");


--
-- Name: idx_swaps_timestamp; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_swaps_timestamp ON ONLY ll_history_schema.swaps USING brin ("timestamp");


--
-- Name: idx_swaps_token_timestamp; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_swaps_token_timestamp ON ONLY ll_history_schema.swaps USING btree (token, "timestamp");


--
-- Name: idx_swaps_token_type_timestamp; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_swaps_token_type_timestamp ON ONLY ll_history_schema.swaps USING btree (token, type, "timestamp");


--
-- Name: idx_swaps_trader_timestamp; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_swaps_trader_timestamp ON ONLY ll_history_schema.swaps USING btree (trader, "timestamp");


--
-- Name: idx_swaps_tx_hash; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_swaps_tx_hash ON ONLY ll_history_schema.swaps USING btree (tx_hash);


--
-- Name: idx_token_24h_volume; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_token_24h_volume ON ll_history_schema.token_24h_volume USING btree (token);


--
-- Name: idx_token_bonded_conflict; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_token_bonded_conflict ON ONLY ll_history_schema.token_bonded_events USING btree (tx_hash, token, "timestamp");


--
-- Name: idx_token_bonded_token; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_token_bonded_token ON ONLY ll_history_schema.token_bonded_events USING btree (token);


--
-- Name: idx_token_burned_conflict; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_token_burned_conflict ON ONLY ll_history_schema.token_burned_events USING btree (tx_hash, token, "timestamp");


--
-- Name: idx_token_burned_token; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_token_burned_token ON ONLY ll_history_schema.token_burned_events USING btree (token);


--
-- Name: idx_token_frozen_conflict; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_token_frozen_conflict ON ONLY ll_history_schema.token_frozen_events USING btree (tx_hash, token, "timestamp");


--
-- Name: idx_token_frozen_token; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_token_frozen_token ON ONLY ll_history_schema.token_frozen_events USING btree (token);


--
-- Name: idx_token_holders_address; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_token_holders_address ON ll_history_schema.token_holders USING btree (holder_address);


--
-- Name: idx_token_holders_balance; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_token_holders_balance ON ll_history_schema.token_holders USING btree (token_address, balance DESC);


--
-- Name: idx_tokens_creation; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_tokens_creation ON ll_history_schema.tokens USING btree (creation_timestamp);


--
-- Name: idx_tokens_creator; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_tokens_creator ON ll_history_schema.tokens USING btree (creator);


--
-- Name: idx_tokens_metadata; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_tokens_metadata ON ll_history_schema.tokens USING gin (metadata);


--
-- Name: idx_tokens_symbol; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_tokens_symbol ON ll_history_schema.tokens USING btree (symbol);


--
-- Name: idx_top_10_pnl; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_top_10_pnl ON ll_history_schema.top_10_pnl USING btree (trader);


--
-- Name: idx_transfer_conflict; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX idx_transfer_conflict ON ONLY ll_history_schema.transfer_events USING btree (tx_hash, token, from_address, to_address, value, "timestamp");


--
-- Name: idx_transfer_from; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_transfer_from ON ONLY ll_history_schema.transfer_events USING btree (from_address);


--
-- Name: idx_transfer_timestamp; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_transfer_timestamp ON ONLY ll_history_schema.transfer_events USING brin ("timestamp");


--
-- Name: idx_transfer_to; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_transfer_to ON ONLY ll_history_schema.transfer_events USING btree (to_address);


--
-- Name: idx_transfer_token; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_transfer_token ON ONLY ll_history_schema.transfer_events USING btree (token);


--
-- Name: idx_wallet_first_interaction_time; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX idx_wallet_first_interaction_time ON ll_history_schema.wallet_first_interaction USING btree (first_interaction_time);


--
-- Name: liquidity_burned_events_y2024_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX liquidity_burned_events_y2024_token_idx ON ll_history_schema.liquidity_burned_events_y2024 USING btree (token);


--
-- Name: liquidity_burned_events_y2024_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX liquidity_burned_events_y2024_tx_hash_token_timestamp_idx ON ll_history_schema.liquidity_burned_events_y2024 USING btree (tx_hash, token, "timestamp");


--
-- Name: liquidity_burned_events_y2025_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX liquidity_burned_events_y2025_token_idx ON ll_history_schema.liquidity_burned_events_y2025 USING btree (token);


--
-- Name: liquidity_burned_events_y2025_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX liquidity_burned_events_y2025_tx_hash_token_timestamp_idx ON ll_history_schema.liquidity_burned_events_y2025 USING btree (tx_hash, token, "timestamp");


--
-- Name: liquidity_burned_events_y2026_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX liquidity_burned_events_y2026_token_idx ON ll_history_schema.liquidity_burned_events_y2026 USING btree (token);


--
-- Name: liquidity_burned_events_y2026_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX liquidity_burned_events_y2026_tx_hash_token_timestamp_idx ON ll_history_schema.liquidity_burned_events_y2026 USING btree (tx_hash, token, "timestamp");


--
-- Name: pair_created_events_y2024_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX pair_created_events_y2024_token_idx ON ll_history_schema.pair_created_events_y2024 USING btree (token);


--
-- Name: pair_created_events_y2024_tx_hash_token_pair_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX pair_created_events_y2024_tx_hash_token_pair_timestamp_idx ON ll_history_schema.pair_created_events_y2024 USING btree (tx_hash, token, pair, "timestamp");


--
-- Name: pair_created_events_y2025_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX pair_created_events_y2025_token_idx ON ll_history_schema.pair_created_events_y2025 USING btree (token);


--
-- Name: pair_created_events_y2025_tx_hash_token_pair_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX pair_created_events_y2025_tx_hash_token_pair_timestamp_idx ON ll_history_schema.pair_created_events_y2025 USING btree (tx_hash, token, pair, "timestamp");


--
-- Name: pair_created_events_y2026_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX pair_created_events_y2026_token_idx ON ll_history_schema.pair_created_events_y2026 USING btree (token);


--
-- Name: pair_created_events_y2026_tx_hash_token_pair_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX pair_created_events_y2026_tx_hash_token_pair_timestamp_idx ON ll_history_schema.pair_created_events_y2026 USING btree (tx_hash, token, pair, "timestamp");


--
-- Name: price_data_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX price_data_timestamp_idx ON ll_history_schema.price_data USING btree ("timestamp" DESC);


--
-- Name: swaps_y2024_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2024_timestamp_idx ON ll_history_schema.swaps_y2024 USING brin ("timestamp");


--
-- Name: swaps_y2024_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2024_token_timestamp_idx ON ll_history_schema.swaps_y2024 USING btree (token, "timestamp");


--
-- Name: swaps_y2024_token_type_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2024_token_type_timestamp_idx ON ll_history_schema.swaps_y2024 USING btree (token, type, "timestamp");


--
-- Name: swaps_y2024_trader_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2024_trader_timestamp_idx ON ll_history_schema.swaps_y2024 USING btree (trader, "timestamp");


--
-- Name: swaps_y2024_tx_hash_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2024_tx_hash_idx ON ll_history_schema.swaps_y2024 USING btree (tx_hash);


--
-- Name: swaps_y2024_tx_hash_type_trader_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX swaps_y2024_tx_hash_type_trader_token_timestamp_idx ON ll_history_schema.swaps_y2024 USING btree (tx_hash, type, trader, token, "timestamp");


--
-- Name: swaps_y2025_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2025_timestamp_idx ON ll_history_schema.swaps_y2025 USING brin ("timestamp");


--
-- Name: swaps_y2025_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2025_token_timestamp_idx ON ll_history_schema.swaps_y2025 USING btree (token, "timestamp");


--
-- Name: swaps_y2025_token_type_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2025_token_type_timestamp_idx ON ll_history_schema.swaps_y2025 USING btree (token, type, "timestamp");


--
-- Name: swaps_y2025_trader_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2025_trader_timestamp_idx ON ll_history_schema.swaps_y2025 USING btree (trader, "timestamp");


--
-- Name: swaps_y2025_tx_hash_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2025_tx_hash_idx ON ll_history_schema.swaps_y2025 USING btree (tx_hash);


--
-- Name: swaps_y2025_tx_hash_type_trader_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX swaps_y2025_tx_hash_type_trader_token_timestamp_idx ON ll_history_schema.swaps_y2025 USING btree (tx_hash, type, trader, token, "timestamp");


--
-- Name: swaps_y2026_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2026_timestamp_idx ON ll_history_schema.swaps_y2026 USING brin ("timestamp");


--
-- Name: swaps_y2026_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2026_token_timestamp_idx ON ll_history_schema.swaps_y2026 USING btree (token, "timestamp");


--
-- Name: swaps_y2026_token_type_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2026_token_type_timestamp_idx ON ll_history_schema.swaps_y2026 USING btree (token, type, "timestamp");


--
-- Name: swaps_y2026_trader_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2026_trader_timestamp_idx ON ll_history_schema.swaps_y2026 USING btree (trader, "timestamp");


--
-- Name: swaps_y2026_tx_hash_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX swaps_y2026_tx_hash_idx ON ll_history_schema.swaps_y2026 USING btree (tx_hash);


--
-- Name: swaps_y2026_tx_hash_type_trader_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX swaps_y2026_tx_hash_type_trader_token_timestamp_idx ON ll_history_schema.swaps_y2026 USING btree (tx_hash, type, trader, token, "timestamp");


--
-- Name: token_bonded_events_y2024_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_bonded_events_y2024_token_idx ON ll_history_schema.token_bonded_events_y2024 USING btree (token);


--
-- Name: token_bonded_events_y2024_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_bonded_events_y2024_tx_hash_token_timestamp_idx ON ll_history_schema.token_bonded_events_y2024 USING btree (tx_hash, token, "timestamp");


--
-- Name: token_bonded_events_y2025_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_bonded_events_y2025_token_idx ON ll_history_schema.token_bonded_events_y2025 USING btree (token);


--
-- Name: token_bonded_events_y2025_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_bonded_events_y2025_tx_hash_token_timestamp_idx ON ll_history_schema.token_bonded_events_y2025 USING btree (tx_hash, token, "timestamp");


--
-- Name: token_bonded_events_y2026_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_bonded_events_y2026_token_idx ON ll_history_schema.token_bonded_events_y2026 USING btree (token);


--
-- Name: token_bonded_events_y2026_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_bonded_events_y2026_tx_hash_token_timestamp_idx ON ll_history_schema.token_bonded_events_y2026 USING btree (tx_hash, token, "timestamp");


--
-- Name: token_burned_events_y2024_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_burned_events_y2024_token_idx ON ll_history_schema.token_burned_events_y2024 USING btree (token);


--
-- Name: token_burned_events_y2024_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_burned_events_y2024_tx_hash_token_timestamp_idx ON ll_history_schema.token_burned_events_y2024 USING btree (tx_hash, token, "timestamp");


--
-- Name: token_burned_events_y2025_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_burned_events_y2025_token_idx ON ll_history_schema.token_burned_events_y2025 USING btree (token);


--
-- Name: token_burned_events_y2025_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_burned_events_y2025_tx_hash_token_timestamp_idx ON ll_history_schema.token_burned_events_y2025 USING btree (tx_hash, token, "timestamp");


--
-- Name: token_burned_events_y2026_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_burned_events_y2026_token_idx ON ll_history_schema.token_burned_events_y2026 USING btree (token);


--
-- Name: token_burned_events_y2026_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_burned_events_y2026_tx_hash_token_timestamp_idx ON ll_history_schema.token_burned_events_y2026 USING btree (tx_hash, token, "timestamp");


--
-- Name: token_frozen_events_y2024_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_frozen_events_y2024_token_idx ON ll_history_schema.token_frozen_events_y2024 USING btree (token);


--
-- Name: token_frozen_events_y2024_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_frozen_events_y2024_tx_hash_token_timestamp_idx ON ll_history_schema.token_frozen_events_y2024 USING btree (tx_hash, token, "timestamp");


--
-- Name: token_frozen_events_y2025_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_frozen_events_y2025_token_idx ON ll_history_schema.token_frozen_events_y2025 USING btree (token);


--
-- Name: token_frozen_events_y2025_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_frozen_events_y2025_tx_hash_token_timestamp_idx ON ll_history_schema.token_frozen_events_y2025 USING btree (tx_hash, token, "timestamp");


--
-- Name: token_frozen_events_y2026_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX token_frozen_events_y2026_token_idx ON ll_history_schema.token_frozen_events_y2026 USING btree (token);


--
-- Name: token_frozen_events_y2026_tx_hash_token_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX token_frozen_events_y2026_tx_hash_token_timestamp_idx ON ll_history_schema.token_frozen_events_y2026 USING btree (tx_hash, token, "timestamp");


--
-- Name: transfer_events_y2024_from_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2024_from_address_idx ON ll_history_schema.transfer_events_y2024 USING btree (from_address);


--
-- Name: transfer_events_y2024_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2024_timestamp_idx ON ll_history_schema.transfer_events_y2024 USING brin ("timestamp");


--
-- Name: transfer_events_y2024_to_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2024_to_address_idx ON ll_history_schema.transfer_events_y2024 USING btree (to_address);


--
-- Name: transfer_events_y2024_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2024_token_idx ON ll_history_schema.transfer_events_y2024 USING btree (token);


--
-- Name: transfer_events_y2024_tx_hash_token_from_address_to_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX transfer_events_y2024_tx_hash_token_from_address_to_address_idx ON ll_history_schema.transfer_events_y2024 USING btree (tx_hash, token, from_address, to_address, value, "timestamp");


--
-- Name: transfer_events_y2025_from_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2025_from_address_idx ON ll_history_schema.transfer_events_y2025 USING btree (from_address);


--
-- Name: transfer_events_y2025_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2025_timestamp_idx ON ll_history_schema.transfer_events_y2025 USING brin ("timestamp");


--
-- Name: transfer_events_y2025_to_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2025_to_address_idx ON ll_history_schema.transfer_events_y2025 USING btree (to_address);


--
-- Name: transfer_events_y2025_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2025_token_idx ON ll_history_schema.transfer_events_y2025 USING btree (token);


--
-- Name: transfer_events_y2025_tx_hash_token_from_address_to_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX transfer_events_y2025_tx_hash_token_from_address_to_address_idx ON ll_history_schema.transfer_events_y2025 USING btree (tx_hash, token, from_address, to_address, value, "timestamp");


--
-- Name: transfer_events_y2026_from_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2026_from_address_idx ON ll_history_schema.transfer_events_y2026 USING btree (from_address);


--
-- Name: transfer_events_y2026_timestamp_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2026_timestamp_idx ON ll_history_schema.transfer_events_y2026 USING brin ("timestamp");


--
-- Name: transfer_events_y2026_to_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2026_to_address_idx ON ll_history_schema.transfer_events_y2026 USING btree (to_address);


--
-- Name: transfer_events_y2026_token_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE INDEX transfer_events_y2026_token_idx ON ll_history_schema.transfer_events_y2026 USING btree (token);


--
-- Name: transfer_events_y2026_tx_hash_token_from_address_to_address_idx; Type: INDEX; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE UNIQUE INDEX transfer_events_y2026_tx_hash_token_from_address_to_address_idx ON ll_history_schema.transfer_events_y2026 USING btree (tx_hash, token, from_address, to_address, value, "timestamp");


--
-- Name: liquidity_burned_events_y2024_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.liquidity_burned_events_pkey ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2024_pkey;


--
-- Name: liquidity_burned_events_y2024_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_liquidity_burned_token ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2024_token_idx;


--
-- Name: liquidity_burned_events_y2024_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_liquidity_burned_conflict ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2024_tx_hash_token_timestamp_idx;


--
-- Name: liquidity_burned_events_y2025_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.liquidity_burned_events_pkey ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2025_pkey;


--
-- Name: liquidity_burned_events_y2025_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_liquidity_burned_token ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2025_token_idx;


--
-- Name: liquidity_burned_events_y2025_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_liquidity_burned_conflict ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2025_tx_hash_token_timestamp_idx;


--
-- Name: liquidity_burned_events_y2026_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.liquidity_burned_events_pkey ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2026_pkey;


--
-- Name: liquidity_burned_events_y2026_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_liquidity_burned_token ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2026_token_idx;


--
-- Name: liquidity_burned_events_y2026_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_liquidity_burned_conflict ATTACH PARTITION ll_history_schema.liquidity_burned_events_y2026_tx_hash_token_timestamp_idx;


--
-- Name: pair_created_events_y2024_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.pair_created_events_pkey ATTACH PARTITION ll_history_schema.pair_created_events_y2024_pkey;


--
-- Name: pair_created_events_y2024_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_pair_created_token ATTACH PARTITION ll_history_schema.pair_created_events_y2024_token_idx;


--
-- Name: pair_created_events_y2024_tx_hash_token_pair_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_pair_created_conflict ATTACH PARTITION ll_history_schema.pair_created_events_y2024_tx_hash_token_pair_timestamp_idx;


--
-- Name: pair_created_events_y2025_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.pair_created_events_pkey ATTACH PARTITION ll_history_schema.pair_created_events_y2025_pkey;


--
-- Name: pair_created_events_y2025_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_pair_created_token ATTACH PARTITION ll_history_schema.pair_created_events_y2025_token_idx;


--
-- Name: pair_created_events_y2025_tx_hash_token_pair_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_pair_created_conflict ATTACH PARTITION ll_history_schema.pair_created_events_y2025_tx_hash_token_pair_timestamp_idx;


--
-- Name: pair_created_events_y2026_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.pair_created_events_pkey ATTACH PARTITION ll_history_schema.pair_created_events_y2026_pkey;


--
-- Name: pair_created_events_y2026_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_pair_created_token ATTACH PARTITION ll_history_schema.pair_created_events_y2026_token_idx;


--
-- Name: pair_created_events_y2026_tx_hash_token_pair_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_pair_created_conflict ATTACH PARTITION ll_history_schema.pair_created_events_y2026_tx_hash_token_pair_timestamp_idx;


--
-- Name: swaps_y2024_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.swaps_pkey ATTACH PARTITION ll_history_schema.swaps_y2024_pkey;


--
-- Name: swaps_y2024_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_timestamp ATTACH PARTITION ll_history_schema.swaps_y2024_timestamp_idx;


--
-- Name: swaps_y2024_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_token_timestamp ATTACH PARTITION ll_history_schema.swaps_y2024_token_timestamp_idx;


--
-- Name: swaps_y2024_token_type_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_token_type_timestamp ATTACH PARTITION ll_history_schema.swaps_y2024_token_type_timestamp_idx;


--
-- Name: swaps_y2024_trader_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_trader_timestamp ATTACH PARTITION ll_history_schema.swaps_y2024_trader_timestamp_idx;


--
-- Name: swaps_y2024_tx_hash_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_tx_hash ATTACH PARTITION ll_history_schema.swaps_y2024_tx_hash_idx;


--
-- Name: swaps_y2024_tx_hash_type_trader_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_conflict_target ATTACH PARTITION ll_history_schema.swaps_y2024_tx_hash_type_trader_token_timestamp_idx;


--
-- Name: swaps_y2025_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.swaps_pkey ATTACH PARTITION ll_history_schema.swaps_y2025_pkey;


--
-- Name: swaps_y2025_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_timestamp ATTACH PARTITION ll_history_schema.swaps_y2025_timestamp_idx;


--
-- Name: swaps_y2025_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_token_timestamp ATTACH PARTITION ll_history_schema.swaps_y2025_token_timestamp_idx;


--
-- Name: swaps_y2025_token_type_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_token_type_timestamp ATTACH PARTITION ll_history_schema.swaps_y2025_token_type_timestamp_idx;


--
-- Name: swaps_y2025_trader_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_trader_timestamp ATTACH PARTITION ll_history_schema.swaps_y2025_trader_timestamp_idx;


--
-- Name: swaps_y2025_tx_hash_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_tx_hash ATTACH PARTITION ll_history_schema.swaps_y2025_tx_hash_idx;


--
-- Name: swaps_y2025_tx_hash_type_trader_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_conflict_target ATTACH PARTITION ll_history_schema.swaps_y2025_tx_hash_type_trader_token_timestamp_idx;


--
-- Name: swaps_y2026_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.swaps_pkey ATTACH PARTITION ll_history_schema.swaps_y2026_pkey;


--
-- Name: swaps_y2026_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_timestamp ATTACH PARTITION ll_history_schema.swaps_y2026_timestamp_idx;


--
-- Name: swaps_y2026_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_token_timestamp ATTACH PARTITION ll_history_schema.swaps_y2026_token_timestamp_idx;


--
-- Name: swaps_y2026_token_type_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_token_type_timestamp ATTACH PARTITION ll_history_schema.swaps_y2026_token_type_timestamp_idx;


--
-- Name: swaps_y2026_trader_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_trader_timestamp ATTACH PARTITION ll_history_schema.swaps_y2026_trader_timestamp_idx;


--
-- Name: swaps_y2026_tx_hash_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_tx_hash ATTACH PARTITION ll_history_schema.swaps_y2026_tx_hash_idx;


--
-- Name: swaps_y2026_tx_hash_type_trader_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_swaps_conflict_target ATTACH PARTITION ll_history_schema.swaps_y2026_tx_hash_type_trader_token_timestamp_idx;


--
-- Name: token_bonded_events_y2024_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_bonded_events_pkey ATTACH PARTITION ll_history_schema.token_bonded_events_y2024_pkey;


--
-- Name: token_bonded_events_y2024_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_bonded_token ATTACH PARTITION ll_history_schema.token_bonded_events_y2024_token_idx;


--
-- Name: token_bonded_events_y2024_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_bonded_conflict ATTACH PARTITION ll_history_schema.token_bonded_events_y2024_tx_hash_token_timestamp_idx;


--
-- Name: token_bonded_events_y2025_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_bonded_events_pkey ATTACH PARTITION ll_history_schema.token_bonded_events_y2025_pkey;


--
-- Name: token_bonded_events_y2025_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_bonded_token ATTACH PARTITION ll_history_schema.token_bonded_events_y2025_token_idx;


--
-- Name: token_bonded_events_y2025_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_bonded_conflict ATTACH PARTITION ll_history_schema.token_bonded_events_y2025_tx_hash_token_timestamp_idx;


--
-- Name: token_bonded_events_y2026_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_bonded_events_pkey ATTACH PARTITION ll_history_schema.token_bonded_events_y2026_pkey;


--
-- Name: token_bonded_events_y2026_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_bonded_token ATTACH PARTITION ll_history_schema.token_bonded_events_y2026_token_idx;


--
-- Name: token_bonded_events_y2026_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_bonded_conflict ATTACH PARTITION ll_history_schema.token_bonded_events_y2026_tx_hash_token_timestamp_idx;


--
-- Name: token_burned_events_y2024_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_burned_events_pkey ATTACH PARTITION ll_history_schema.token_burned_events_y2024_pkey;


--
-- Name: token_burned_events_y2024_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_burned_token ATTACH PARTITION ll_history_schema.token_burned_events_y2024_token_idx;


--
-- Name: token_burned_events_y2024_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_burned_conflict ATTACH PARTITION ll_history_schema.token_burned_events_y2024_tx_hash_token_timestamp_idx;


--
-- Name: token_burned_events_y2025_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_burned_events_pkey ATTACH PARTITION ll_history_schema.token_burned_events_y2025_pkey;


--
-- Name: token_burned_events_y2025_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_burned_token ATTACH PARTITION ll_history_schema.token_burned_events_y2025_token_idx;


--
-- Name: token_burned_events_y2025_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_burned_conflict ATTACH PARTITION ll_history_schema.token_burned_events_y2025_tx_hash_token_timestamp_idx;


--
-- Name: token_burned_events_y2026_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_burned_events_pkey ATTACH PARTITION ll_history_schema.token_burned_events_y2026_pkey;


--
-- Name: token_burned_events_y2026_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_burned_token ATTACH PARTITION ll_history_schema.token_burned_events_y2026_token_idx;


--
-- Name: token_burned_events_y2026_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_burned_conflict ATTACH PARTITION ll_history_schema.token_burned_events_y2026_tx_hash_token_timestamp_idx;


--
-- Name: token_frozen_events_y2024_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_frozen_events_pkey ATTACH PARTITION ll_history_schema.token_frozen_events_y2024_pkey;


--
-- Name: token_frozen_events_y2024_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_frozen_token ATTACH PARTITION ll_history_schema.token_frozen_events_y2024_token_idx;


--
-- Name: token_frozen_events_y2024_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_frozen_conflict ATTACH PARTITION ll_history_schema.token_frozen_events_y2024_tx_hash_token_timestamp_idx;


--
-- Name: token_frozen_events_y2025_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_frozen_events_pkey ATTACH PARTITION ll_history_schema.token_frozen_events_y2025_pkey;


--
-- Name: token_frozen_events_y2025_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_frozen_token ATTACH PARTITION ll_history_schema.token_frozen_events_y2025_token_idx;


--
-- Name: token_frozen_events_y2025_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_frozen_conflict ATTACH PARTITION ll_history_schema.token_frozen_events_y2025_tx_hash_token_timestamp_idx;


--
-- Name: token_frozen_events_y2026_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.token_frozen_events_pkey ATTACH PARTITION ll_history_schema.token_frozen_events_y2026_pkey;


--
-- Name: token_frozen_events_y2026_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_frozen_token ATTACH PARTITION ll_history_schema.token_frozen_events_y2026_token_idx;


--
-- Name: token_frozen_events_y2026_tx_hash_token_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_token_frozen_conflict ATTACH PARTITION ll_history_schema.token_frozen_events_y2026_tx_hash_token_timestamp_idx;


--
-- Name: transfer_events_y2024_from_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_from ATTACH PARTITION ll_history_schema.transfer_events_y2024_from_address_idx;


--
-- Name: transfer_events_y2024_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.transfer_events_pkey ATTACH PARTITION ll_history_schema.transfer_events_y2024_pkey;


--
-- Name: transfer_events_y2024_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_timestamp ATTACH PARTITION ll_history_schema.transfer_events_y2024_timestamp_idx;


--
-- Name: transfer_events_y2024_to_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_to ATTACH PARTITION ll_history_schema.transfer_events_y2024_to_address_idx;


--
-- Name: transfer_events_y2024_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_token ATTACH PARTITION ll_history_schema.transfer_events_y2024_token_idx;


--
-- Name: transfer_events_y2024_tx_hash_token_from_address_to_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_conflict ATTACH PARTITION ll_history_schema.transfer_events_y2024_tx_hash_token_from_address_to_address_idx;


--
-- Name: transfer_events_y2025_from_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_from ATTACH PARTITION ll_history_schema.transfer_events_y2025_from_address_idx;


--
-- Name: transfer_events_y2025_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.transfer_events_pkey ATTACH PARTITION ll_history_schema.transfer_events_y2025_pkey;


--
-- Name: transfer_events_y2025_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_timestamp ATTACH PARTITION ll_history_schema.transfer_events_y2025_timestamp_idx;


--
-- Name: transfer_events_y2025_to_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_to ATTACH PARTITION ll_history_schema.transfer_events_y2025_to_address_idx;


--
-- Name: transfer_events_y2025_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_token ATTACH PARTITION ll_history_schema.transfer_events_y2025_token_idx;


--
-- Name: transfer_events_y2025_tx_hash_token_from_address_to_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_conflict ATTACH PARTITION ll_history_schema.transfer_events_y2025_tx_hash_token_from_address_to_address_idx;


--
-- Name: transfer_events_y2026_from_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_from ATTACH PARTITION ll_history_schema.transfer_events_y2026_from_address_idx;


--
-- Name: transfer_events_y2026_pkey; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.transfer_events_pkey ATTACH PARTITION ll_history_schema.transfer_events_y2026_pkey;


--
-- Name: transfer_events_y2026_timestamp_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_timestamp ATTACH PARTITION ll_history_schema.transfer_events_y2026_timestamp_idx;


--
-- Name: transfer_events_y2026_to_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_to ATTACH PARTITION ll_history_schema.transfer_events_y2026_to_address_idx;


--
-- Name: transfer_events_y2026_token_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_token ATTACH PARTITION ll_history_schema.transfer_events_y2026_token_idx;


--
-- Name: transfer_events_y2026_tx_hash_token_from_address_to_address_idx; Type: INDEX ATTACH; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER INDEX ll_history_schema.idx_transfer_conflict ATTACH PARTITION ll_history_schema.transfer_events_y2026_tx_hash_token_from_address_to_address_idx;


--
-- Name: price_data ts_insert_blocker; Type: TRIGGER; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TRIGGER ts_insert_blocker BEFORE INSERT ON ll_history_schema.price_data FOR EACH ROW EXECUTE FUNCTION _timescaledb_functions.insert_blocker();


--
-- Name: token_holders update_holder_count_trigger; Type: TRIGGER; Schema: ll_history_schema; Owner: liquidlaunch_history
--

CREATE TRIGGER update_holder_count_trigger AFTER INSERT OR DELETE OR UPDATE ON ll_history_schema.token_holders FOR EACH ROW EXECUTE FUNCTION ll_history_schema.update_token_holder_count();


--
-- Name: liquidity_burned_events liquidity_burned_events_token_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ll_history_schema.liquidity_burned_events
    ADD CONSTRAINT liquidity_burned_events_token_fkey FOREIGN KEY (token) REFERENCES ll_history_schema.tokens(address);


--
-- Name: pair_created_events pair_created_events_token_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ll_history_schema.pair_created_events
    ADD CONSTRAINT pair_created_events_token_fkey FOREIGN KEY (token) REFERENCES ll_history_schema.tokens(address);


--
-- Name: realtime_token_data realtime_token_data_token_address_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.realtime_token_data
    ADD CONSTRAINT realtime_token_data_token_address_fkey FOREIGN KEY (token_address) REFERENCES ll_history_schema.tokens(address) ON DELETE CASCADE;


--
-- Name: swaps swaps_token_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ll_history_schema.swaps
    ADD CONSTRAINT swaps_token_fkey FOREIGN KEY (token) REFERENCES ll_history_schema.tokens(address);


--
-- Name: token_bonded_events token_bonded_events_token_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ll_history_schema.token_bonded_events
    ADD CONSTRAINT token_bonded_events_token_fkey FOREIGN KEY (token) REFERENCES ll_history_schema.tokens(address);


--
-- Name: token_burned_events token_burned_events_token_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ll_history_schema.token_burned_events
    ADD CONSTRAINT token_burned_events_token_fkey FOREIGN KEY (token) REFERENCES ll_history_schema.tokens(address);


--
-- Name: token_frozen_events token_frozen_events_token_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ll_history_schema.token_frozen_events
    ADD CONSTRAINT token_frozen_events_token_fkey FOREIGN KEY (token) REFERENCES ll_history_schema.tokens(address);


--
-- Name: token_holders token_holders_token_address_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_holders
    ADD CONSTRAINT token_holders_token_address_fkey FOREIGN KEY (token_address) REFERENCES ll_history_schema.tokens(address) ON DELETE CASCADE;


--
-- Name: token_metadata_history token_metadata_history_token_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ONLY ll_history_schema.token_metadata_history
    ADD CONSTRAINT token_metadata_history_token_fkey FOREIGN KEY (token) REFERENCES ll_history_schema.tokens(address);


--
-- Name: transfer_events transfer_events_token_fkey; Type: FK CONSTRAINT; Schema: ll_history_schema; Owner: liquidlaunch_history
--

ALTER TABLE ll_history_schema.transfer_events
    ADD CONSTRAINT transfer_events_token_fkey FOREIGN KEY (token) REFERENCES ll_history_schema.tokens(address);


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO liquidlaunch_history;


--
-- Name: SCHEMA _timescaledb_cache; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA _timescaledb_cache TO liquidlaunch_history;


--
-- Name: SCHEMA _timescaledb_catalog; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA _timescaledb_catalog TO liquidlaunch_history;


--
-- Name: SCHEMA _timescaledb_config; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA _timescaledb_config TO liquidlaunch_history;


--
-- Name: SCHEMA _timescaledb_internal; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA _timescaledb_internal TO liquidlaunch_history;


--
-- Name: SCHEMA pg_catalog; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA pg_catalog TO liquidlaunch_history;


--
-- PostgreSQL database dump complete
--

