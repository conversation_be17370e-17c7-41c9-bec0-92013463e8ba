{"name": "liquidlabs-ohlcv-ws-api", "version": "2.0.0", "description": "OHLCV WebSocket API with Token Bonding Progress - Refactored with Fastify", "main": "server-new.js", "scripts": {"start": "node server-new.js", "dev": "nodemon server-new.js", "cluster": "pm2 start ecosystem.config.js", "cluster:dev": "pm2 start ecosystem.config.js --env development", "cluster:stop": "pm2 stop liquidlabs-ohlcv-api", "cluster:restart": "pm2 restart liquidlabs-ohlcv-api", "cluster:reload": "pm2 reload liquidlabs-ohlcv-api", "cluster:delete": "pm2 delete liquidlabs-ohlcv-api", "cluster:logs": "pm2 logs liquidlabs-ohlcv-api", "cluster:monit": "pm2 monit", "cluster:status": "pm2 status", "test": "npm run test:unit", "test:unit": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["fastify", "websocket", "ohlcv", "crypto", "trading", "real-time", "api"], "author": "LiquidLabs", "license": "MIT", "dependencies": {"@fastify/compress": "^6.4.0", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^8.0.3", "@fastify/swagger": "^8.12.0", "@fastify/swagger-ui": "^1.10.0", "axios": "^1.6.0", "dotenv": "^16.3.1", "fastify": "^4.24.3", "fastify-plugin": "^4.5.1", "fastify-socket.io": "^4.0.0", "pg": "^8.11.3", "pino-pretty": "^10.2.3", "pm2": "^5.3.0", "socket.io": "^4.7.4"}, "devDependencies": {"eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.3.1", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/liquidlabs/ohlcv-ws-api.git"}, "bugs": {"url": "https://github.com/liquidlabs/ohlcv-ws-api/issues"}, "homepage": "https://github.com/liquidlabs/ohlcv-ws-api#readme"}