require('dotenv').config();

const fastify = require('fastify')({
  logger: true
});

// Register Swagger
fastify.register(require('@fastify/swagger'), {
  openapi: {
    info: {
      title: 'Donkey API',
      description: 'API for Donkey Protocol',
      version: '1.0.0'
    },
    servers: [
      {
        url: 'https://donkey-api.liquidlaunch.app',
        description: 'Production server'
      }
    ],
    tags: [
      {
        name: 'token',
        description: 'Token related endpoints'
      },
      {
        name: 'wallet',
        description: 'Wallet related endpoints'
      }
    ]
  }
});

fastify.register(require('@fastify/swagger-ui'), {
  routePrefix: '/documentation',
  uiConfig: {
    docExpansion: 'full',
    deepLinking: false
  },
  uiHooks: {
    onRequest: function (request, reply, next) { next(); },
    preHandler: function (request, reply, next) { next(); }
  },
  staticCSP: true,
  transformStaticCSP: (header) => header
});

// Register security plugins
fastify.register(require('@fastify/cors'), {
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST'],
  credentials: true
});

fastify.register(require('@fastify/helmet'), {
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "validator.swagger.io"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"]
    }
  }
});

// Register plugins
fastify.register(require('./plugins/database'));

// Register routes
fastify.register(require('./routes/tokens-clean'));
fastify.register(require('./routes/wallet'));

// Run the server
const start = async () => {
  try {
    await fastify.listen({ port: process.env.PORT || 3000, host: '0.0.0.0' });
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start(); 