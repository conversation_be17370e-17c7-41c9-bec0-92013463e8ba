// server-minimal.js - Minimal server without WebSocket for testing
const fastify = require('fastify')({
  logger: {
    level: 'info',
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true
      }
    }
  }
});

require('dotenv').config();

// Error handler
fastify.setErrorHandler((error, request, reply) => {
  fastify.log.error(error);
  
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal Server Error';
  
  reply.status(statusCode).send({
    error: message,
    statusCode
  });
});

async function setupMinimalServer() {
  try {
    // CORS support
    await fastify.register(require('@fastify/cors'), {
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST'],
      credentials: true
    });

    // Rate limiting
    await fastify.register(require('@fastify/rate-limit'), {
      max: 100,
      timeWindow: '1 minute',
      skipOnError: true
    });

    // Helmet for security
    await fastify.register(require('@fastify/helmet'), {
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "validator.swagger.io"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"]
        }
      }
    });

    // Compression
    await fastify.register(require('@fastify/compress'), {
      global: true
    });

    // Swagger documentation
    await fastify.register(require('@fastify/swagger'), {
      openapi: {
        openapi: '3.0.0',
        info: {
          title: 'OHLCV WebSocket API',
          description: 'API documentation for the OHLCV WebSocket server.',
          version: '2.0.0'
        }
      }
    });

    // Swagger UI
    await fastify.register(require('@fastify/swagger-ui'), {
      routePrefix: '/documentation',
      uiConfig: {
        docExpansion: 'list',
        deepLinking: false
      },
      staticCSP: false
    });

    // Register database plugin only
    await fastify.register(require('./plugins/database'));

    // Register routes (without WebSocket-dependent routes)
    await fastify.register(require('./routes/tokens-clean'));
    await fastify.register(require('./routes/ohlcv'));

    // Health check endpoint
    fastify.get('/health', {
      schema: {
        description: 'Health check endpoint',
        tags: ['system'],
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
              uptime: { type: 'number' },
              version: { type: 'string' }
            }
          }
        }
      }
    }, async (request, reply) => {
      try {
        await fastify.pool.query('SELECT 1');
        
        return {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          version: '2.0.0'
        };
      } catch (error) {
        reply.code(503);
        return {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          version: '2.0.0',
          error: error.message
        };
      }
    });

    // 404 handler
    fastify.setNotFoundHandler((request, reply) => {
      reply.code(404).send({
        error: 'Route not found',
        statusCode: 404,
        message: `Route ${request.method} ${request.url} not found`
      });
    });

    // Start the server
    const port = process.env.PORT || 3050;
    const host = process.env.HOST || '0.0.0.0';

    await fastify.listen({ port, host });
    
    fastify.log.info(`🚀 Minimal server listening on http://${host}:${port}`);
    fastify.log.info(`📚 Documentation available at http://${host}:${port}/documentation`);
    fastify.log.info(`🔧 Health check available at http://${host}:${port}/health`);

  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
}

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  fastify.log.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    await fastify.close();
    process.exit(0);
  } catch (err) {
    fastify.log.error('Error during shutdown:', err);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  fastify.log.fatal('Uncaught exception:', err);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  fastify.log.fatal('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
setupMinimalServer(); 