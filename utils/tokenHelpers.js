const axios = require('axios');

// Constants for bonding calculations
const BONDING_CONSTANTS = {
  INITIAL_SUPPLY: 1_000_000_000,
  TARGET_RESERVES: 350_700_000,
  TARGET_BOUGHT: 649_300_000, // INITIAL_SUPPLY - TARGET_RESERVES
  TARGET_MCAP_HYPE_FOR_BONDING: 2439,
  MIN_HYPE_RESERVES: 300, // 0% progress
  MAX_HYPE_RESERVES: 855.35, // 100% progress
  HYPE_RANGE: 555.35 // MAX_HYPE_RESERVES - MIN_HYPE_RESERVES
};

// Cache for HYPE price
let hypePriceCache = {
  price: '0',
  timestamp: 0
};

const CACHE_TTL = 30000; // 30 seconds in milliseconds

/**
 * Format token amount using decimals (similar to ethers.utils.formatUnits)
 */
function formatTokenAmount(amount, decimals = 18) {
  if (!amount || amount === '0' || amount === 0) return '0';

  const amountStr = amount.toString();
  const decimalPlaces = parseInt(decimals) || 18;

  // If the amount is already a decimal (contains a dot), check if it needs formatting
  if (amountStr.includes('.')) {
    const value = parseFloat(amountStr);
    // If the value is very small (< 1), it's likely already formatted
    // If it's very large (> 10^15), it might need decimal formatting
    if (value < 1 || value.toString().length <= 15) {
      return value.toString();
    }
  }

  // Convert from wei-like format to human readable
  const rawValue = parseFloat(amountStr);

  // If the value is already reasonable (not in wei format), return as is
  if (rawValue < Math.pow(10, decimalPlaces - 3)) {
    return rawValue.toString();
  }

  // Otherwise, format using decimals
  const divisor = Math.pow(10, decimalPlaces);
  const formatted = rawValue / divisor;

  return formatted.toString();
}

/**
 * Get current HYPE price from Hyperliquid API with caching
 */
async function getHypePrice() {
  const now = Date.now();
  
  // Return cached price if it's still valid
  if (now - hypePriceCache.timestamp < CACHE_TTL) {
    return hypePriceCache.price;
  }

  try {
    const response = await axios.post('https://api.hyperliquid.xyz/info', {
      type: 'allMids'
    });
    const price = response.data['HYPE'] || '0';
    
    // Update cache
    hypePriceCache = {
      price,
      timestamp: now
    };
    
    return price;
  } catch (error) {
    console.error('Error fetching HYPE price:', error);
    // If we have a cached price, return it even if expired
    if (hypePriceCache.price !== '0') {
      return hypePriceCache.price;
    }
    return '0';
  }
}

/**
 * Calculate bonding progress for a token
 */
function calculateBondingProgress(tokenReserves, hypeReserves, totalSupply, hypePrice = '0', swaps = []) {
  const reserves = parseFloat(tokenReserves) || BONDING_CONSTANTS.INITIAL_SUPPLY;
  const hype = parseFloat(hypeReserves) || 0;
  const supply = parseFloat(totalSupply) || BONDING_CONSTANTS.INITIAL_SUPPLY;
  const hypePriceFloat = parseFloat(hypePrice);

  // Calculate tokens sold from swaps
  const tokensSold = swaps.reduce((total, swap) => {
    if (swap.type === 'purchase') {
      return total + parseFloat(swap.token_amount || 0);
    }
    return total;
  }, 0);

  const tokensSoldPercentage = (tokensSold / BONDING_CONSTANTS.INITIAL_SUPPLY) * 100;

  // Calculate bonding progress based on HYPE reserves
  let progress = 0;
  if (hype > BONDING_CONSTANTS.MIN_HYPE_RESERVES) {
    progress = ((hype - BONDING_CONSTANTS.MIN_HYPE_RESERVES) / BONDING_CONSTANTS.HYPE_RANGE) * 100;
    progress = Math.min(progress, 100);
  }

  // Calculate market cap at bonding (100% progress)
  const bondsAtMcap = (BONDING_CONSTANTS.TARGET_MCAP_HYPE_FOR_BONDING * hypePriceFloat).toFixed(2);

  // Token is bonded only when progress reaches 100%
  const isBonded = progress >= 100;

  return {
    progress: progress.toFixed(2),
    hypeProgress: progress.toFixed(2), // Same as progress since it's based on HYPE
    tokensSold: tokensSold.toString(),
    tokensSoldPercentage: tokensSoldPercentage.toFixed(2),
    hypeNeededFor100: BONDING_CONSTANTS.MAX_HYPE_RESERVES.toString(),
    currentHypeInPool: hype.toFixed(18),
    bondsAtMcap,
    isBonded,
    constants: {
      initialSupply: BONDING_CONSTANTS.INITIAL_SUPPLY.toString(),
      targetReserves: BONDING_CONSTANTS.TARGET_RESERVES.toString(),
      targetBought: BONDING_CONSTANTS.TARGET_BOUGHT.toString(),
      minHypeReserves: BONDING_CONSTANTS.MIN_HYPE_RESERVES.toString(),
      maxHypeReserves: BONDING_CONSTANTS.MAX_HYPE_RESERVES.toString(),
      targetMcapHypeForBonding: BONDING_CONSTANTS.TARGET_MCAP_HYPE_FOR_BONDING.toString()
    }
  };
}

/**
 * Transform raw token data from database to API response format
 */
function transformTokenData(token, hypePrice) {
  const priceHype = parseFloat(token.price_hype || 0);
  const marketCapHype = parseFloat(token.market_cap_hype || 0);
  const hypeReserves = parseFloat(token.hype_reserves || 0);
  const tokenReserves = parseFloat(token.token_reserves || 0);
  const totalSupply = parseFloat(token.total_supply || 0);
  const tokensSold = parseFloat(token.tokens_sold || 0);
  const decimals = parseInt(token.decimals) || 18;

  // Format token reserves using the token's decimals (like ethers.utils.formatUnits)
  const formattedTokenReserves = formatTokenAmount(token.token_reserves, decimals);
  const formattedTotalSupply = formatTokenAmount(token.total_supply, decimals);

  // Use database values for bonded/frozen status, fallback to calculated values if not available
  const isBonded = token.bonded !== undefined ? Boolean(token.bonded) : (hypeReserves >= BONDING_CONSTANTS.MAX_HYPE_RESERVES);
  const isFrozen = token.frozen !== undefined ? Boolean(token.frozen) : false;

  const bonding = calculateBondingProgress(formattedTokenReserves, hypeReserves, formattedTotalSupply, hypePrice, [{ type: 'purchase', token_amount: tokensSold.toString() }]);
  const hypePriceFloat = parseFloat(hypePrice);

  // Handle trading statistics from the query
  const buyCount = parseInt(token.buy_count || 0);
  const sellCount = parseInt(token.sell_count || 0);
  const uniqueBuyers = parseInt(token.unique_buyers || 0);
  const uniqueSellers = parseInt(token.unique_sellers || 0);
  const buyVolume = parseFloat(token.buy_volume || 0);
  const sellVolume = parseFloat(token.sell_volume || 0);
  const totalVolume = buyVolume + sellVolume;
  const priceChange = parseFloat(token.price_change || 0);
  const firstPrice = parseFloat(token.first_price || 0);
  const lastPrice = parseFloat(token.last_price || 0);

  return {
    address: token.address,
    name: token.name || '',
    symbol: token.symbol || '',
    decimals: token.decimals || 18,
    metadata: {
      name: token.name || '',
      symbol: token.symbol || '',
      discord: token.metadata?.discord || '',
      twitter: token.metadata?.twitter || '',
      website: token.metadata?.website || '',
      telegram: token.metadata?.telegram || '',
      image_uri: token.metadata?.image_uri || '',
      description: token.metadata?.description || '',
      creationTimestamp: {
        hex: '0x' + parseInt(token.creation_timestamp || 0).toString(16),
        type: 'BigNumber'
      }
    },
    supplyData: {
      tokenReserves: formattedTokenReserves,
      hypeReserves: hypeReserves,
      totalSupply: formattedTotalSupply,
      holders: token.holder_count?.toString() || '0'
    },
    creator: token.creator || '',
    creationTimestamp: parseInt(token.creation_timestamp || 0),
    price: {
      usd: (priceHype * hypePriceFloat).toFixed(6),
      hype: priceHype.toFixed(18)
    },
    marketCap: {
      usd: (marketCapHype * hypePriceFloat).toFixed(2),
      hype: marketCapHype.toFixed(18)
    },
    liquidity: {
      usd: (hypeReserves * hypePriceFloat).toFixed(2),
      hype: hypeReserves.toFixed(18)
    },
    totalSupply: formattedTotalSupply,
    holderCount: token.holder_count?.toString() || '0',
    tradingStats: {
      buys: buyCount,
      sells: sellCount,
      uniqueBuyers: uniqueBuyers,
      uniqueSellers: uniqueSellers,
      buyVolume: {
        hype: buyVolume.toFixed(18),
        usd: (buyVolume * hypePriceFloat).toFixed(2)
      },
      sellVolume: {
        hype: sellVolume.toFixed(18),
        usd: (sellVolume * hypePriceFloat).toFixed(2)
      },
      totalVolume: {
        hype: totalVolume.toFixed(18),
        usd: (totalVolume * hypePriceFloat).toFixed(2)
      },
      priceChange: priceChange,
      firstPrice: firstPrice,
      lastPrice: lastPrice
    },
    // Keep legacy timeframes for backward compatibility
    timeframes: {
      '24h': {
        buys: buyCount,
        sells: sellCount,
        volume: totalVolume,
        priceChange: priceChange,
        uniqueBuyers: uniqueBuyers,
        uniqueSellers: uniqueSellers,
        buyVolume: buyVolume,
        sellVolume: sellVolume,
        firstPrice: firstPrice,
        lastPrice: lastPrice
      }
    },
    holders: token.top_holders || [],
    top10HoldersPercentage: token.top10_holders_percentage ? token.top10_holders_percentage.toString() + '%' : '0%',
    isBonded: isBonded,
    isFrozen: isFrozen,
    pair: null,
    latestActivityTimestamp: parseInt(token.last_swap_timestamp || 0),
    // Include sorting fields for debugging/display
    swaps_count: buyCount + sellCount,
    volume_24h: totalVolume,
    trades_24h: buyCount + sellCount,
    price_change_24h: priceChange,
    financials: {
      price: {
        usd: (priceHype * hypePriceFloat).toFixed(6),
        hype: priceHype.toFixed(18)
      },
      marketCap: {
        usd: (marketCapHype * hypePriceFloat).toFixed(2),
        hype: marketCapHype.toFixed(18)
      },
      liquidity: {
        usd: (hypeReserves * hypePriceFloat).toFixed(2),
        hype: hypeReserves.toFixed(18)
      },
      volume24h: {
        usd: (totalVolume * hypePriceFloat).toFixed(2),
        hype: totalVolume.toFixed(18)
      },
      priceChange24h: priceChange.toString(),
      trades24h: {
        buys: buyCount,
        sells: sellCount,
        total: buyCount + sellCount,
        uniqueBuyers: uniqueBuyers,
        uniqueSellers: uniqueSellers,
        buyVolume: buyVolume,
        sellVolume: sellVolume
      }
    },
    bonding: {
      ...bonding,
      isBonded: isBonded  // Override the calculated value with database value
    }
  };
}

/**
 * Validate token address format
 */
function isValidTokenAddress(address) {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

module.exports = {
  BONDING_CONSTANTS,
  getHypePrice,
  calculateBondingProgress,
  transformTokenData,
  isValidTokenAddress,
  formatTokenAmount
}; 